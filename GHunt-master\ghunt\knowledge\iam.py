permissions = [
    "accessapproval.requests.approve",
    "accessapproval.requests.dismiss",
    "accessapproval.requests.get",
    "accessapproval.requests.invalidate",
    "accessapproval.requests.list",
    "accessapproval.serviceAccounts.get",
    "accessapproval.settings.delete",
    "accessapproval.settings.get",
    "accessapproval.settings.update",
    "actions.agent.claimContentProvider",
    "actions.agent.get",
    "actions.agent.update",
    "actions.agentVersions.create",
    "actions.agentVersions.delete",
    "actions.agentVersions.deploy",
    "actions.agentVersions.get",
    "actions.agentVersions.list",
    "aiplatform.annotationSpecs.create",
    "aiplatform.annotationSpecs.delete",
    "aiplatform.annotationSpecs.get",
    "aiplatform.annotationSpecs.list",
    "aiplatform.annotationSpecs.update",
    "aiplatform.annotations.create",
    "aiplatform.annotations.delete",
    "aiplatform.annotations.get",
    "aiplatform.annotations.list",
    "aiplatform.annotations.update",
    "aiplatform.artifacts.create",
    "aiplatform.artifacts.delete",
    "aiplatform.artifacts.get",
    "aiplatform.artifacts.list",
    "aiplatform.artifacts.update",
    "aiplatform.batchPredictionJobs.cancel",
    "aiplatform.batchPredictionJobs.create",
    "aiplatform.batchPredictionJobs.delete",
    "aiplatform.batchPredictionJobs.get",
    "aiplatform.batchPredictionJobs.list",
    "aiplatform.contexts.addContextArtifactsAndExecutions",
    "aiplatform.contexts.addContextChildren",
    "aiplatform.contexts.create",
    "aiplatform.contexts.delete",
    "aiplatform.contexts.get",
    "aiplatform.contexts.list",
    "aiplatform.contexts.queryContextLineageSubgraph",
    "aiplatform.contexts.update",
    "aiplatform.customJobs.cancel",
    "aiplatform.customJobs.create",
    "aiplatform.customJobs.delete",
    "aiplatform.customJobs.get",
    "aiplatform.customJobs.list",
    "aiplatform.dataItems.create",
    "aiplatform.dataItems.delete",
    "aiplatform.dataItems.get",
    "aiplatform.dataItems.list",
    "aiplatform.dataItems.update",
    "aiplatform.dataLabelingJobs.cancel",
    "aiplatform.dataLabelingJobs.create",
    "aiplatform.dataLabelingJobs.delete",
    "aiplatform.dataLabelingJobs.get",
    "aiplatform.dataLabelingJobs.list",
    "aiplatform.datasets.create",
    "aiplatform.datasets.delete",
    "aiplatform.datasets.export",
    "aiplatform.datasets.get",
    "aiplatform.datasets.import",
    "aiplatform.datasets.list",
    "aiplatform.datasets.update",
    "aiplatform.deploymentResourcePools.create",
    "aiplatform.deploymentResourcePools.delete",
    "aiplatform.deploymentResourcePools.get",
    "aiplatform.deploymentResourcePools.list",
    "aiplatform.deploymentResourcePools.queryDeployedModels",
    "aiplatform.deploymentResourcePools.update",
    "aiplatform.edgeDeploymentJobs.create",
    "aiplatform.edgeDeploymentJobs.delete",
    "aiplatform.edgeDeploymentJobs.get",
    "aiplatform.edgeDeploymentJobs.list",
    "aiplatform.edgeDeviceDebugInfo.get",
    "aiplatform.edgeDevices.create",
    "aiplatform.edgeDevices.delete",
    "aiplatform.edgeDevices.get",
    "aiplatform.edgeDevices.list",
    "aiplatform.edgeDevices.update",
    "aiplatform.endpoints.create",
    "aiplatform.endpoints.delete",
    "aiplatform.endpoints.deploy",
    "aiplatform.endpoints.explain",
    "aiplatform.endpoints.get",
    "aiplatform.endpoints.list",
    "aiplatform.endpoints.predict",
    "aiplatform.endpoints.undeploy",
    "aiplatform.endpoints.update",
    "aiplatform.entityTypes.create",
    "aiplatform.entityTypes.delete",
    "aiplatform.entityTypes.deleteFeatureValues",
    "aiplatform.entityTypes.exportFeatureValues",
    "aiplatform.entityTypes.get",
    "aiplatform.entityTypes.getIamPolicy",
    "aiplatform.entityTypes.importFeatureValues",
    "aiplatform.entityTypes.list",
    "aiplatform.entityTypes.readFeatureValues",
    "aiplatform.entityTypes.setIamPolicy",
    "aiplatform.entityTypes.streamingReadFeatureValues",
    "aiplatform.entityTypes.update",
    "aiplatform.entityTypes.writeFeatureValues",
    "aiplatform.executions.addExecutionEvents",
    "aiplatform.executions.create",
    "aiplatform.executions.delete",
    "aiplatform.executions.get",
    "aiplatform.executions.list",
    "aiplatform.executions.queryExecutionInputsAndOutputs",
    "aiplatform.executions.update",
    "aiplatform.features.create",
    "aiplatform.features.delete",
    "aiplatform.features.get",
    "aiplatform.features.list",
    "aiplatform.features.update",
    "aiplatform.featurestores.batchReadFeatureValues",
    "aiplatform.featurestores.create",
    "aiplatform.featurestores.delete",
    "aiplatform.featurestores.exportFeatures",
    "aiplatform.featurestores.get",
    "aiplatform.featurestores.getIamPolicy",
    "aiplatform.featurestores.importFeatures",
    "aiplatform.featurestores.list",
    "aiplatform.featurestores.readFeatures",
    "aiplatform.featurestores.setIamPolicy",
    "aiplatform.featurestores.update",
    "aiplatform.featurestores.writeFeatures",
    "aiplatform.humanInTheLoops.create",
    "aiplatform.humanInTheLoops.delete",
    "aiplatform.humanInTheLoops.get",
    "aiplatform.humanInTheLoops.list",
    "aiplatform.humanInTheLoops.queryAnnotationStats",
    "aiplatform.humanInTheLoops.send",
    "aiplatform.humanInTheLoops.update",
    "aiplatform.hyperparameterTuningJobs.cancel",
    "aiplatform.hyperparameterTuningJobs.create",
    "aiplatform.hyperparameterTuningJobs.delete",
    "aiplatform.hyperparameterTuningJobs.get",
    "aiplatform.hyperparameterTuningJobs.list",
    "aiplatform.indexEndpoints.create",
    "aiplatform.indexEndpoints.delete",
    "aiplatform.indexEndpoints.deploy",
    "aiplatform.indexEndpoints.get",
    "aiplatform.indexEndpoints.list",
    "aiplatform.indexEndpoints.undeploy",
    "aiplatform.indexEndpoints.update",
    "aiplatform.indexes.create",
    "aiplatform.indexes.delete",
    "aiplatform.indexes.get",
    "aiplatform.indexes.list",
    "aiplatform.indexes.update",
    "aiplatform.locations.get",
    "aiplatform.locations.list",
    "aiplatform.metadataSchemas.create",
    "aiplatform.metadataSchemas.delete",
    "aiplatform.metadataSchemas.get",
    "aiplatform.metadataSchemas.list",
    "aiplatform.metadataStores.create",
    "aiplatform.metadataStores.delete",
    "aiplatform.metadataStores.get",
    "aiplatform.metadataStores.list",
    "aiplatform.migratableResources.migrate",
    "aiplatform.migratableResources.search",
    "aiplatform.modelDeploymentMonitoringJobs.create",
    "aiplatform.modelDeploymentMonitoringJobs.delete",
    "aiplatform.modelDeploymentMonitoringJobs.get",
    "aiplatform.modelDeploymentMonitoringJobs.list",
    "aiplatform.modelDeploymentMonitoringJobs.pause",
    "aiplatform.modelDeploymentMonitoringJobs.resume",
    "aiplatform.modelDeploymentMonitoringJobs.searchStatsAnomalies",
    "aiplatform.modelDeploymentMonitoringJobs.update",
    "aiplatform.modelEvaluationSlices.get",
    "aiplatform.modelEvaluationSlices.list",
    "aiplatform.modelEvaluations.exportEvaluatedDataItems",
    "aiplatform.modelEvaluations.get",
    "aiplatform.modelEvaluations.list",
    "aiplatform.models.delete",
    "aiplatform.models.export",
    "aiplatform.models.get",
    "aiplatform.models.list",
    "aiplatform.models.update",
    "aiplatform.models.upload",
    "aiplatform.nasJobs.cancel",
    "aiplatform.nasJobs.create",
    "aiplatform.nasJobs.delete",
    "aiplatform.nasJobs.get",
    "aiplatform.nasJobs.list",
    "aiplatform.nasTrialDetails.get",
    "aiplatform.nasTrialDetails.list",
    "aiplatform.operations.list",
    "aiplatform.pipelineJobs.cancel",
    "aiplatform.pipelineJobs.create",
    "aiplatform.pipelineJobs.delete",
    "aiplatform.pipelineJobs.get",
    "aiplatform.pipelineJobs.list",
    "aiplatform.specialistPools.create",
    "aiplatform.specialistPools.delete",
    "aiplatform.specialistPools.get",
    "aiplatform.specialistPools.list",
    "aiplatform.specialistPools.update",
    "aiplatform.studies.create",
    "aiplatform.studies.delete",
    "aiplatform.studies.get",
    "aiplatform.studies.list",
    "aiplatform.studies.update",
    "aiplatform.tensorboardExperiments.create",
    "aiplatform.tensorboardExperiments.delete",
    "aiplatform.tensorboardExperiments.get",
    "aiplatform.tensorboardExperiments.list",
    "aiplatform.tensorboardExperiments.update",
    "aiplatform.tensorboardExperiments.write",
    "aiplatform.tensorboardRuns.batchCreate",
    "aiplatform.tensorboardRuns.create",
    "aiplatform.tensorboardRuns.delete",
    "aiplatform.tensorboardRuns.get",
    "aiplatform.tensorboardRuns.list",
    "aiplatform.tensorboardRuns.update",
    "aiplatform.tensorboardRuns.write",
    "aiplatform.tensorboardTimeSeries.batchCreate",
    "aiplatform.tensorboardTimeSeries.batchRead",
    "aiplatform.tensorboardTimeSeries.create",
    "aiplatform.tensorboardTimeSeries.delete",
    "aiplatform.tensorboardTimeSeries.get",
    "aiplatform.tensorboardTimeSeries.list",
    "aiplatform.tensorboardTimeSeries.read",
    "aiplatform.tensorboardTimeSeries.update",
    "aiplatform.tensorboards.create",
    "aiplatform.tensorboards.delete",
    "aiplatform.tensorboards.get",
    "aiplatform.tensorboards.list",
    "aiplatform.tensorboards.recordAccess",
    "aiplatform.tensorboards.update",
    "aiplatform.trainingPipelines.cancel",
    "aiplatform.trainingPipelines.create",
    "aiplatform.trainingPipelines.delete",
    "aiplatform.trainingPipelines.get",
    "aiplatform.trainingPipelines.list",
    "aiplatform.trials.create",
    "aiplatform.trials.delete",
    "aiplatform.trials.get",
    "aiplatform.trials.list",
    "aiplatform.trials.update",
    "alloydb.backups.create",
    "alloydb.backups.delete",
    "alloydb.backups.get",
    "alloydb.backups.list",
    "alloydb.backups.update",
    "alloydb.clusters.create",
    "alloydb.clusters.delete",
    "alloydb.clusters.generateClientCertificate",
    "alloydb.clusters.get",
    "alloydb.clusters.list",
    "alloydb.clusters.update",
    "alloydb.instances.connect",
    "alloydb.instances.create",
    "alloydb.instances.delete",
    "alloydb.instances.failover",
    "alloydb.instances.get",
    "alloydb.instances.list",
    "alloydb.instances.restart",
    "alloydb.instances.update",
    "alloydb.locations.get",
    "alloydb.locations.list",
    "alloydb.operations.cancel",
    "alloydb.operations.delete",
    "alloydb.operations.get",
    "alloydb.operations.list",
    "alloydb.supportedDatabaseFlags.get",
    "alloydb.supportedDatabaseFlags.list",
    "analyticshub.dataExchanges.create",
    "analyticshub.dataExchanges.delete",
    "analyticshub.dataExchanges.get",
    "analyticshub.dataExchanges.getIamPolicy",
    "analyticshub.dataExchanges.list",
    "analyticshub.dataExchanges.setIamPolicy",
    "analyticshub.dataExchanges.update",
    "analyticshub.listings.create",
    "analyticshub.listings.delete",
    "analyticshub.listings.get",
    "analyticshub.listings.getIamPolicy",
    "analyticshub.listings.list",
    "analyticshub.listings.setIamPolicy",
    "analyticshub.listings.subscribe",
    "analyticshub.listings.update",
    "androidmanagement.enterprises.manage",
    "apigateway.apiconfigs.create",
    "apigateway.apiconfigs.delete",
    "apigateway.apiconfigs.get",
    "apigateway.apiconfigs.getIamPolicy",
    "apigateway.apiconfigs.list",
    "apigateway.apiconfigs.setIamPolicy",
    "apigateway.apiconfigs.update",
    "apigateway.apis.create",
    "apigateway.apis.delete",
    "apigateway.apis.get",
    "apigateway.apis.getIamPolicy",
    "apigateway.apis.list",
    "apigateway.apis.setIamPolicy",
    "apigateway.apis.update",
    "apigateway.gateways.create",
    "apigateway.gateways.delete",
    "apigateway.gateways.get",
    "apigateway.gateways.getIamPolicy",
    "apigateway.gateways.list",
    "apigateway.gateways.setIamPolicy",
    "apigateway.gateways.update",
    "apigateway.locations.get",
    "apigateway.locations.list",
    "apigateway.operations.cancel",
    "apigateway.operations.delete",
    "apigateway.operations.get",
    "apigateway.operations.list",
    "apigee.apiproductattributes.createOrUpdateAll",
    "apigee.apiproductattributes.delete",
    "apigee.apiproductattributes.get",
    "apigee.apiproductattributes.list",
    "apigee.apiproductattributes.update",
    "apigee.apiproducts.create",
    "apigee.apiproducts.delete",
    "apigee.apiproducts.get",
    "apigee.apiproducts.list",
    "apigee.apiproducts.update",
    "apigee.appkeys.create",
    "apigee.appkeys.delete",
    "apigee.appkeys.get",
    "apigee.appkeys.manage",
    "apigee.apps.get",
    "apigee.apps.list",
    "apigee.archivedeployments.create",
    "apigee.archivedeployments.delete",
    "apigee.archivedeployments.download",
    "apigee.archivedeployments.get",
    "apigee.archivedeployments.list",
    "apigee.archivedeployments.update",
    "apigee.archivedeployments.upload",
    "apigee.caches.delete",
    "apigee.caches.list",
    "apigee.canaryevaluations.create",
    "apigee.canaryevaluations.get",
    "apigee.datacollectors.create",
    "apigee.datacollectors.delete",
    "apigee.datacollectors.get",
    "apigee.datacollectors.list",
    "apigee.datacollectors.update",
    "apigee.datalocation.get",
    "apigee.datastores.create",
    "apigee.datastores.delete",
    "apigee.datastores.get",
    "apigee.datastores.list",
    "apigee.datastores.update",
    "apigee.deployments.create",
    "apigee.deployments.delete",
    "apigee.deployments.get",
    "apigee.deployments.list",
    "apigee.deployments.update",
    "apigee.developerappattributes.createOrUpdateAll",
    "apigee.developerappattributes.delete",
    "apigee.developerappattributes.get",
    "apigee.developerappattributes.list",
    "apigee.developerappattributes.update",
    "apigee.developerapps.create",
    "apigee.developerapps.delete",
    "apigee.developerapps.get",
    "apigee.developerapps.list",
    "apigee.developerapps.manage",
    "apigee.developerattributes.createOrUpdateAll",
    "apigee.developerattributes.delete",
    "apigee.developerattributes.get",
    "apigee.developerattributes.list",
    "apigee.developerattributes.update",
    "apigee.developerbalances.adjust",
    "apigee.developerbalances.get",
    "apigee.developerbalances.update",
    "apigee.developermonetizationconfigs.get",
    "apigee.developermonetizationconfigs.update",
    "apigee.developers.create",
    "apigee.developers.delete",
    "apigee.developers.get",
    "apigee.developers.list",
    "apigee.developers.update",
    "apigee.developersubscriptions.create",
    "apigee.developersubscriptions.get",
    "apigee.developersubscriptions.list",
    "apigee.developersubscriptions.update",
    "apigee.endpointattachments.create",
    "apigee.endpointattachments.delete",
    "apigee.endpointattachments.get",
    "apigee.endpointattachments.list",
    "apigee.envgroupattachments.create",
    "apigee.envgroupattachments.delete",
    "apigee.envgroupattachments.get",
    "apigee.envgroupattachments.list",
    "apigee.envgroups.create",
    "apigee.envgroups.delete",
    "apigee.envgroups.get",
    "apigee.envgroups.list",
    "apigee.envgroups.update",
    "apigee.environments.create",
    "apigee.environments.delete",
    "apigee.environments.get",
    "apigee.environments.getDataLocation",
    "apigee.environments.getIamPolicy",
    "apigee.environments.getStats",
    "apigee.environments.list",
    "apigee.environments.manageRuntime",
    "apigee.environments.setIamPolicy",
    "apigee.environments.update",
    "apigee.exports.create",
    "apigee.exports.get",
    "apigee.exports.list",
    "apigee.flowhooks.attachSharedFlow",
    "apigee.flowhooks.detachSharedFlow",
    "apigee.flowhooks.getSharedFlow",
    "apigee.flowhooks.list",
    "apigee.hostqueries.create",
    "apigee.hostqueries.get",
    "apigee.hostqueries.list",
    "apigee.hostsecurityreports.create",
    "apigee.hostsecurityreports.get",
    "apigee.hostsecurityreports.list",
    "apigee.hoststats.get",
    "apigee.ingressconfigs.get",
    "apigee.instanceattachments.create",
    "apigee.instanceattachments.delete",
    "apigee.instanceattachments.get",
    "apigee.instanceattachments.list",
    "apigee.instances.create",
    "apigee.instances.delete",
    "apigee.instances.get",
    "apigee.instances.list",
    "apigee.instances.reportStatus",
    "apigee.instances.update",
    "apigee.keystorealiases.create",
    "apigee.keystorealiases.delete",
    "apigee.keystorealiases.exportCertificate",
    "apigee.keystorealiases.generateCSR",
    "apigee.keystorealiases.get",
    "apigee.keystorealiases.list",
    "apigee.keystorealiases.update",
    "apigee.keystores.create",
    "apigee.keystores.delete",
    "apigee.keystores.export",
    "apigee.keystores.get",
    "apigee.keystores.list",
    "apigee.keyvaluemapentries.create",
    "apigee.keyvaluemapentries.delete",
    "apigee.keyvaluemapentries.get",
    "apigee.keyvaluemapentries.list",
    "apigee.keyvaluemaps.create",
    "apigee.keyvaluemaps.delete",
    "apigee.keyvaluemaps.list",
    "apigee.maskconfigs.get",
    "apigee.maskconfigs.update",
    "apigee.operations.get",
    "apigee.operations.list",
    "apigee.organizations.create",
    "apigee.organizations.delete",
    "apigee.organizations.get",
    "apigee.organizations.list",
    "apigee.organizations.update",
    "apigee.portals.create",
    "apigee.portals.delete",
    "apigee.portals.get",
    "apigee.portals.list",
    "apigee.portals.update",
    "apigee.projects.migrate",
    "apigee.projects.previewMigration",
    "apigee.projects.update",
    "apigee.proxies.create",
    "apigee.proxies.delete",
    "apigee.proxies.get",
    "apigee.proxies.list",
    "apigee.proxies.update",
    "apigee.proxyrevisions.delete",
    "apigee.proxyrevisions.deploy",
    "apigee.proxyrevisions.get",
    "apigee.proxyrevisions.list",
    "apigee.proxyrevisions.undeploy",
    "apigee.proxyrevisions.update",
    "apigee.queries.create",
    "apigee.queries.get",
    "apigee.queries.list",
    "apigee.rateplans.create",
    "apigee.rateplans.delete",
    "apigee.rateplans.get",
    "apigee.rateplans.list",
    "apigee.rateplans.update",
    "apigee.references.create",
    "apigee.references.delete",
    "apigee.references.get",
    "apigee.references.list",
    "apigee.references.update",
    "apigee.reports.create",
    "apigee.reports.delete",
    "apigee.reports.get",
    "apigee.reports.list",
    "apigee.reports.update",
    "apigee.resourcefiles.create",
    "apigee.resourcefiles.delete",
    "apigee.resourcefiles.get",
    "apigee.resourcefiles.list",
    "apigee.resourcefiles.update",
    "apigee.runtimeconfigs.get",
    "apigee.securityProfileEnvironments.computeScore",
    "apigee.securityProfileEnvironments.create",
    "apigee.securityProfileEnvironments.delete",
    "apigee.securityProfiles.get",
    "apigee.securityProfiles.list",
    "apigee.securityStats.queryTabularStats",
    "apigee.securityStats.queryTimeSeriesStats",
    "apigee.securityreports.create",
    "apigee.securityreports.get",
    "apigee.securityreports.list",
    "apigee.sharedflowrevisions.delete",
    "apigee.sharedflowrevisions.deploy",
    "apigee.sharedflowrevisions.get",
    "apigee.sharedflowrevisions.list",
    "apigee.sharedflowrevisions.undeploy",
    "apigee.sharedflowrevisions.update",
    "apigee.sharedflows.create",
    "apigee.sharedflows.delete",
    "apigee.sharedflows.get",
    "apigee.sharedflows.list",
    "apigee.targetservers.create",
    "apigee.targetservers.delete",
    "apigee.targetservers.get",
    "apigee.targetservers.list",
    "apigee.targetservers.update",
    "apigee.traceconfig.get",
    "apigee.traceconfig.update",
    "apigee.traceconfigoverrides.create",
    "apigee.traceconfigoverrides.delete",
    "apigee.traceconfigoverrides.get",
    "apigee.traceconfigoverrides.list",
    "apigee.traceconfigoverrides.update",
    "apigee.tracesessions.create",
    "apigee.tracesessions.delete",
    "apigee.tracesessions.get",
    "apigee.tracesessions.list",
    "apigeeconnect.connections.list",
    "apigeeconnect.endpoints.connect",
    "apigeeregistry.apis.create",
    "apigeeregistry.apis.delete",
    "apigeeregistry.apis.get",
    "apigeeregistry.apis.getIamPolicy",
    "apigeeregistry.apis.list",
    "apigeeregistry.apis.setIamPolicy",
    "apigeeregistry.apis.update",
    "apigeeregistry.artifacts.create",
    "apigeeregistry.artifacts.delete",
    "apigeeregistry.artifacts.get",
    "apigeeregistry.artifacts.getIamPolicy",
    "apigeeregistry.artifacts.list",
    "apigeeregistry.artifacts.setIamPolicy",
    "apigeeregistry.artifacts.update",
    "apigeeregistry.deployments.create",
    "apigeeregistry.deployments.delete",
    "apigeeregistry.deployments.get",
    "apigeeregistry.deployments.list",
    "apigeeregistry.deployments.update",
    "apigeeregistry.instances.get",
    "apigeeregistry.instances.update",
    "apigeeregistry.locations.get",
    "apigeeregistry.locations.list",
    "apigeeregistry.operations.cancel",
    "apigeeregistry.operations.delete",
    "apigeeregistry.operations.get",
    "apigeeregistry.operations.list",
    "apigeeregistry.specs.create",
    "apigeeregistry.specs.delete",
    "apigeeregistry.specs.get",
    "apigeeregistry.specs.getIamPolicy",
    "apigeeregistry.specs.list",
    "apigeeregistry.specs.setIamPolicy",
    "apigeeregistry.specs.update",
    "apigeeregistry.versions.create",
    "apigeeregistry.versions.delete",
    "apigeeregistry.versions.get",
    "apigeeregistry.versions.getIamPolicy",
    "apigeeregistry.versions.list",
    "apigeeregistry.versions.setIamPolicy",
    "apigeeregistry.versions.update",
    "apikeys.keys.create",
    "apikeys.keys.delete",
    "apikeys.keys.get",
    "apikeys.keys.getKeyString",
    "apikeys.keys.list",
    "apikeys.keys.lookup",
    "apikeys.keys.undelete",
    "apikeys.keys.update",
    "appengine.applications.create",
    "appengine.applications.get",
    "appengine.applications.update",
    "appengine.instances.delete",
    "appengine.instances.get",
    "appengine.instances.list",
    "appengine.memcache.addKey",
    "appengine.memcache.flush",
    "appengine.memcache.get",
    "appengine.memcache.getKey",
    "appengine.memcache.list",
    "appengine.memcache.update",
    "appengine.operations.get",
    "appengine.operations.list",
    "appengine.runtimes.actAsAdmin",
    "appengine.services.delete",
    "appengine.services.get",
    "appengine.services.list",
    "appengine.services.update",
    "appengine.versions.create",
    "appengine.versions.delete",
    "appengine.versions.get",
    "appengine.versions.getFileContents",
    "appengine.versions.list",
    "appengine.versions.update",
    "artifactregistry.aptartifacts.create",
    "artifactregistry.dockerimages.get",
    "artifactregistry.dockerimages.list",
    "artifactregistry.files.get",
    "artifactregistry.files.list",
    "artifactregistry.kfpartifacts.create",
    "artifactregistry.locations.get",
    "artifactregistry.locations.list",
    "artifactregistry.mavenartifacts.get",
    "artifactregistry.mavenartifacts.list",
    "artifactregistry.npmpackages.get",
    "artifactregistry.npmpackages.list",
    "artifactregistry.packages.delete",
    "artifactregistry.packages.get",
    "artifactregistry.packages.list",
    "artifactregistry.projectsettings.get",
    "artifactregistry.projectsettings.update",
    "artifactregistry.pythonpackages.get",
    "artifactregistry.pythonpackages.list",
    "artifactregistry.repositories.create",
    "artifactregistry.repositories.createTagBinding",
    "artifactregistry.repositories.delete",
    "artifactregistry.repositories.deleteArtifacts",
    "artifactregistry.repositories.deleteTagBinding",
    "artifactregistry.repositories.downloadArtifacts",
    "artifactregistry.repositories.get",
    "artifactregistry.repositories.getIamPolicy",
    "artifactregistry.repositories.list",
    "artifactregistry.repositories.listEffectiveTags",
    "artifactregistry.repositories.listTagBindings",
    "artifactregistry.repositories.setIamPolicy",
    "artifactregistry.repositories.update",
    "artifactregistry.repositories.uploadArtifacts",
    "artifactregistry.tags.create",
    "artifactregistry.tags.delete",
    "artifactregistry.tags.get",
    "artifactregistry.tags.list",
    "artifactregistry.tags.update",
    "artifactregistry.versions.delete",
    "artifactregistry.versions.get",
    "artifactregistry.versions.list",
    "artifactregistry.yumartifacts.create",
    "automl.annotationSpecs.create",
    "automl.annotationSpecs.delete",
    "automl.annotationSpecs.get",
    "automl.annotationSpecs.list",
    "automl.annotationSpecs.update",
    "automl.annotations.approve",
    "automl.annotations.create",
    "automl.annotations.list",
    "automl.annotations.manipulate",
    "automl.annotations.reject",
    "automl.columnSpecs.get",
    "automl.columnSpecs.list",
    "automl.columnSpecs.update",
    "automl.datasets.create",
    "automl.datasets.delete",
    "automl.datasets.export",
    "automl.datasets.get",
    "automl.datasets.getIamPolicy",
    "automl.datasets.import",
    "automl.datasets.list",
    "automl.datasets.setIamPolicy",
    "automl.datasets.update",
    "automl.examples.delete",
    "automl.examples.get",
    "automl.examples.list",
    "automl.examples.update",
    "automl.files.delete",
    "automl.files.list",
    "automl.humanAnnotationTasks.create",
    "automl.humanAnnotationTasks.delete",
    "automl.humanAnnotationTasks.get",
    "automl.humanAnnotationTasks.list",
    "automl.locations.get",
    "automl.locations.getIamPolicy",
    "automl.locations.list",
    "automl.locations.setIamPolicy",
    "automl.modelEvaluations.create",
    "automl.modelEvaluations.get",
    "automl.modelEvaluations.list",
    "automl.models.create",
    "automl.models.delete",
    "automl.models.deploy",
    "automl.models.export",
    "automl.models.get",
    "automl.models.getIamPolicy",
    "automl.models.list",
    "automl.models.predict",
    "automl.models.setIamPolicy",
    "automl.models.undeploy",
    "automl.operations.cancel",
    "automl.operations.delete",
    "automl.operations.get",
    "automl.operations.list",
    "automl.tableSpecs.get",
    "automl.tableSpecs.list",
    "automl.tableSpecs.update",
    "automlrecommendations.apiKeys.create",
    "automlrecommendations.apiKeys.delete",
    "automlrecommendations.apiKeys.list",
    "automlrecommendations.catalogItems.create",
    "automlrecommendations.catalogItems.delete",
    "automlrecommendations.catalogItems.get",
    "automlrecommendations.catalogItems.list",
    "automlrecommendations.catalogItems.update",
    "automlrecommendations.catalogs.getStats",
    "automlrecommendations.catalogs.list",
    "automlrecommendations.catalogs.update",
    "automlrecommendations.eventStores.getStats",
    "automlrecommendations.events.create",
    "automlrecommendations.events.list",
    "automlrecommendations.events.purge",
    "automlrecommendations.events.rejoin",
    "automlrecommendations.placements.create",
    "automlrecommendations.placements.delete",
    "automlrecommendations.placements.getStats",
    "automlrecommendations.placements.list",
    "automlrecommendations.recommendations.create",
    "automlrecommendations.recommendations.delete",
    "automlrecommendations.recommendations.list",
    "automlrecommendations.recommendations.pause",
    "automlrecommendations.recommendations.resume",
    "automlrecommendations.recommendations.update",
    "autoscaling.sites.getIamPolicy",
    "autoscaling.sites.readRecommendations",
    "autoscaling.sites.setIamPolicy",
    "autoscaling.sites.writeMetrics",
    "autoscaling.sites.writeState",
    "axt.labels.get",
    "axt.labels.set",
    "backupdr.locations.get",
    "backupdr.locations.list",
    "backupdr.managementServers.backupAccess",
    "backupdr.managementServers.create",
    "backupdr.managementServers.delete",
    "backupdr.managementServers.get",
    "backupdr.managementServers.getIamPolicy",
    "backupdr.managementServers.list",
    "backupdr.managementServers.manageInternalACL",
    "backupdr.managementServers.setIamPolicy",
    "backupdr.operations.cancel",
    "backupdr.operations.delete",
    "backupdr.operations.get",
    "backupdr.operations.list",
    "baremetalsolution.instancequotas.list",
    "baremetalsolution.instances.attachNetwork",
    "baremetalsolution.instances.attachVolume",
    "baremetalsolution.instances.create",
    "baremetalsolution.instances.detachLun",
    "baremetalsolution.instances.detachNetwork",
    "baremetalsolution.instances.detachVolume",
    "baremetalsolution.instances.disableInteractiveSerialConsole",
    "baremetalsolution.instances.enableInteractiveSerialConsole",
    "baremetalsolution.instances.get",
    "baremetalsolution.instances.list",
    "baremetalsolution.instances.reset",
    "baremetalsolution.instances.start",
    "baremetalsolution.instances.stop",
    "baremetalsolution.instances.update",
    "baremetalsolution.luns.create",
    "baremetalsolution.luns.delete",
    "baremetalsolution.luns.get",
    "baremetalsolution.luns.list",
    "baremetalsolution.luns.update",
    "baremetalsolution.networkquotas.list",
    "baremetalsolution.networks.create",
    "baremetalsolution.networks.delete",
    "baremetalsolution.networks.get",
    "baremetalsolution.networks.list",
    "baremetalsolution.networks.update",
    "baremetalsolution.nfsshares.create",
    "baremetalsolution.nfsshares.delete",
    "baremetalsolution.nfsshares.get",
    "baremetalsolution.nfsshares.list",
    "baremetalsolution.nfsshares.update",
    "baremetalsolution.snapshotschedulepolicies.create",
    "baremetalsolution.snapshotschedulepolicies.delete",
    "baremetalsolution.snapshotschedulepolicies.get",
    "baremetalsolution.snapshotschedulepolicies.list",
    "baremetalsolution.snapshotschedulepolicies.update",
    "baremetalsolution.sshKeys.create",
    "baremetalsolution.sshKeys.delete",
    "baremetalsolution.sshKeys.list",
    "baremetalsolution.volumequotas.list",
    "baremetalsolution.volumes.create",
    "baremetalsolution.volumes.delete",
    "baremetalsolution.volumes.get",
    "baremetalsolution.volumes.list",
    "baremetalsolution.volumes.resize",
    "baremetalsolution.volumes.update",
    "baremetalsolution.volumesnapshots.create",
    "baremetalsolution.volumesnapshots.delete",
    "baremetalsolution.volumesnapshots.get",
    "baremetalsolution.volumesnapshots.list",
    "baremetalsolution.volumesnapshots.restore",
    "batch.jobs.create",
    "batch.jobs.delete",
    "batch.jobs.get",
    "batch.jobs.list",
    "batch.locations.get",
    "batch.locations.list",
    "batch.operations.get",
    "batch.operations.list",
    "batch.states.report",
    "batch.tasks.get",
    "batch.tasks.list",
    "beyondcorp.appConnections.create",
    "beyondcorp.appConnections.delete",
    "beyondcorp.appConnections.get",
    "beyondcorp.appConnections.getIamPolicy",
    "beyondcorp.appConnections.list",
    "beyondcorp.appConnections.setIamPolicy",
    "beyondcorp.appConnections.update",
    "beyondcorp.appConnectors.create",
    "beyondcorp.appConnectors.delete",
    "beyondcorp.appConnectors.get",
    "beyondcorp.appConnectors.getIamPolicy",
    "beyondcorp.appConnectors.list",
    "beyondcorp.appConnectors.reportStatus",
    "beyondcorp.appConnectors.setIamPolicy",
    "beyondcorp.appConnectors.update",
    "beyondcorp.appGateways.create",
    "beyondcorp.appGateways.delete",
    "beyondcorp.appGateways.get",
    "beyondcorp.appGateways.getIamPolicy",
    "beyondcorp.appGateways.list",
    "beyondcorp.appGateways.setIamPolicy",
    "beyondcorp.appGateways.update",
    "beyondcorp.clientConnectorServices.access",
    "beyondcorp.clientConnectorServices.create",
    "beyondcorp.clientConnectorServices.delete",
    "beyondcorp.clientConnectorServices.get",
    "beyondcorp.clientConnectorServices.getIamPolicy",
    "beyondcorp.clientConnectorServices.list",
    "beyondcorp.clientConnectorServices.setIamPolicy",
    "beyondcorp.clientConnectorServices.update",
    "beyondcorp.clientGateways.create",
    "beyondcorp.clientGateways.delete",
    "beyondcorp.clientGateways.get",
    "beyondcorp.clientGateways.getIamPolicy",
    "beyondcorp.clientGateways.list",
    "beyondcorp.clientGateways.setIamPolicy",
    "beyondcorp.locations.get",
    "beyondcorp.locations.list",
    "beyondcorp.operations.cancel",
    "beyondcorp.operations.delete",
    "beyondcorp.operations.get",
    "beyondcorp.operations.list",
    "bigquery.bireservations.get",
    "bigquery.bireservations.update",
    "bigquery.capacityCommitments.create",
    "bigquery.capacityCommitments.delete",
    "bigquery.capacityCommitments.get",
    "bigquery.capacityCommitments.list",
    "bigquery.capacityCommitments.update",
    "bigquery.config.get",
    "bigquery.config.update",
    "bigquery.connections.create",
    "bigquery.connections.delegate",
    "bigquery.connections.delete",
    "bigquery.connections.get",
    "bigquery.connections.getIamPolicy",
    "bigquery.connections.list",
    "bigquery.connections.setIamPolicy",
    "bigquery.connections.update",
    "bigquery.connections.updateTag",
    "bigquery.connections.use",
    "bigquery.dataPolicies.create",
    "bigquery.dataPolicies.delete",
    "bigquery.dataPolicies.get",
    "bigquery.dataPolicies.getIamPolicy",
    "bigquery.dataPolicies.list",
    "bigquery.dataPolicies.maskedGet",
    "bigquery.dataPolicies.setIamPolicy",
    "bigquery.dataPolicies.update",
    "bigquery.datasets.create",
    "bigquery.datasets.createTagBinding",
    "bigquery.datasets.delete",
    "bigquery.datasets.deleteTagBinding",
    "bigquery.datasets.get",
    "bigquery.datasets.getIamPolicy",
    "bigquery.datasets.link",
    "bigquery.datasets.listTagBindings",
    "bigquery.datasets.setIamPolicy",
    "bigquery.datasets.update",
    "bigquery.datasets.updateTag",
    "bigquery.jobs.create",
    "bigquery.jobs.delete",
    "bigquery.jobs.get",
    "bigquery.jobs.list",
    "bigquery.jobs.listAll",
    "bigquery.jobs.listExecutionMetadata",
    "bigquery.jobs.update",
    "bigquery.models.create",
    "bigquery.models.delete",
    "bigquery.models.export",
    "bigquery.models.getData",
    "bigquery.models.getMetadata",
    "bigquery.models.list",
    "bigquery.models.updateData",
    "bigquery.models.updateMetadata",
    "bigquery.models.updateTag",
    "bigquery.readsessions.create",
    "bigquery.readsessions.getData",
    "bigquery.readsessions.update",
    "bigquery.reservationAssignments.create",
    "bigquery.reservationAssignments.delete",
    "bigquery.reservationAssignments.list",
    "bigquery.reservationAssignments.search",
    "bigquery.reservations.create",
    "bigquery.reservations.delete",
    "bigquery.reservations.get",
    "bigquery.reservations.list",
    "bigquery.reservations.update",
    "bigquery.routines.create",
    "bigquery.routines.delete",
    "bigquery.routines.get",
    "bigquery.routines.list",
    "bigquery.routines.update",
    "bigquery.routines.updateTag",
    "bigquery.rowAccessPolicies.create",
    "bigquery.rowAccessPolicies.delete",
    "bigquery.rowAccessPolicies.getFilteredData",
    "bigquery.rowAccessPolicies.getIamPolicy",
    "bigquery.rowAccessPolicies.list",
    "bigquery.rowAccessPolicies.overrideTimeTravelRestrictions",
    "bigquery.rowAccessPolicies.setIamPolicy",
    "bigquery.rowAccessPolicies.update",
    "bigquery.savedqueries.create",
    "bigquery.savedqueries.delete",
    "bigquery.savedqueries.get",
    "bigquery.savedqueries.list",
    "bigquery.savedqueries.update",
    "bigquery.tables.create",
    "bigquery.tables.createIndex",
    "bigquery.tables.createSnapshot",
    "bigquery.tables.delete",
    "bigquery.tables.deleteIndex",
    "bigquery.tables.deleteSnapshot",
    "bigquery.tables.export",
    "bigquery.tables.get",
    "bigquery.tables.getData",
    "bigquery.tables.getIamPolicy",
    "bigquery.tables.list",
    "bigquery.tables.restoreSnapshot",
    "bigquery.tables.setCategory",
    "bigquery.tables.setIamPolicy",
    "bigquery.tables.update",
    "bigquery.tables.updateData",
    "bigquery.tables.updateTag",
    "bigquery.transfers.get",
    "bigquery.transfers.update",
    "bigquerymigration.locations.get",
    "bigquerymigration.locations.list",
    "bigquerymigration.subtaskTypes.executeTask",
    "bigquerymigration.subtasks.create",
    "bigquerymigration.subtasks.executeTask",
    "bigquerymigration.subtasks.get",
    "bigquerymigration.subtasks.list",
    "bigquerymigration.taskTypes.orchestrateTask",
    "bigquerymigration.translation.translate",
    "bigquerymigration.workflows.create",
    "bigquerymigration.workflows.delete",
    "bigquerymigration.workflows.get",
    "bigquerymigration.workflows.list",
    "bigquerymigration.workflows.orchestrateTask",
    "bigquerymigration.workflows.update",
    "bigquerymigration.workflows.writeLogs",
    "bigtable.appProfiles.create",
    "bigtable.appProfiles.delete",
    "bigtable.appProfiles.get",
    "bigtable.appProfiles.list",
    "bigtable.appProfiles.update",
    "bigtable.backups.create",
    "bigtable.backups.delete",
    "bigtable.backups.get",
    "bigtable.backups.getIamPolicy",
    "bigtable.backups.list",
    "bigtable.backups.read",
    "bigtable.backups.restore",
    "bigtable.backups.setIamPolicy",
    "bigtable.backups.update",
    "bigtable.clusters.create",
    "bigtable.clusters.delete",
    "bigtable.clusters.get",
    "bigtable.clusters.list",
    "bigtable.clusters.update",
    "bigtable.hotTablets.list",
    "bigtable.instances.create",
    "bigtable.instances.createTagBinding",
    "bigtable.instances.delete",
    "bigtable.instances.deleteTagBinding",
    "bigtable.instances.get",
    "bigtable.instances.getIamPolicy",
    "bigtable.instances.list",
    "bigtable.instances.listEffectiveTags",
    "bigtable.instances.listTagBindings",
    "bigtable.instances.ping",
    "bigtable.instances.setIamPolicy",
    "bigtable.instances.update",
    "bigtable.keyvisualizer.get",
    "bigtable.keyvisualizer.list",
    "bigtable.locations.list",
    "bigtable.tables.checkConsistency",
    "bigtable.tables.create",
    "bigtable.tables.delete",
    "bigtable.tables.generateConsistencyToken",
    "bigtable.tables.get",
    "bigtable.tables.getIamPolicy",
    "bigtable.tables.list",
    "bigtable.tables.mutateRows",
    "bigtable.tables.readRows",
    "bigtable.tables.sampleRowKeys",
    "bigtable.tables.setIamPolicy",
    "bigtable.tables.undelete",
    "bigtable.tables.update",
    "billing.resourceCosts.get",
    "binaryauthorization.attestors.create",
    "binaryauthorization.attestors.delete",
    "binaryauthorization.attestors.get",
    "binaryauthorization.attestors.getIamPolicy",
    "binaryauthorization.attestors.list",
    "binaryauthorization.attestors.setIamPolicy",
    "binaryauthorization.attestors.update",
    "binaryauthorization.attestors.verifyImageAttested",
    "binaryauthorization.continuousValidationConfig.get",
    "binaryauthorization.continuousValidationConfig.getIamPolicy",
    "binaryauthorization.continuousValidationConfig.setIamPolicy",
    "binaryauthorization.continuousValidationConfig.update",
    "binaryauthorization.platformPolicies.create",
    "binaryauthorization.platformPolicies.delete",
    "binaryauthorization.platformPolicies.evaluatePolicy",
    "binaryauthorization.platformPolicies.get",
    "binaryauthorization.platformPolicies.list",
    "binaryauthorization.platformPolicies.replace",
    "binaryauthorization.policy.evaluatePolicy",
    "binaryauthorization.policy.get",
    "binaryauthorization.policy.getIamPolicy",
    "binaryauthorization.policy.setIamPolicy",
    "binaryauthorization.policy.update",
    "carestudio.patients.get",
    "carestudio.patients.list",
    "certificatemanager.certissuanceconfigs.create",
    "certificatemanager.certissuanceconfigs.delete",
    "certificatemanager.certissuanceconfigs.get",
    "certificatemanager.certissuanceconfigs.list",
    "certificatemanager.certissuanceconfigs.update",
    "certificatemanager.certissuanceconfigs.use",
    "certificatemanager.certmapentries.create",
    "certificatemanager.certmapentries.delete",
    "certificatemanager.certmapentries.get",
    "certificatemanager.certmapentries.getIamPolicy",
    "certificatemanager.certmapentries.list",
    "certificatemanager.certmapentries.setIamPolicy",
    "certificatemanager.certmapentries.update",
    "certificatemanager.certmaps.create",
    "certificatemanager.certmaps.delete",
    "certificatemanager.certmaps.get",
    "certificatemanager.certmaps.getIamPolicy",
    "certificatemanager.certmaps.list",
    "certificatemanager.certmaps.setIamPolicy",
    "certificatemanager.certmaps.update",
    "certificatemanager.certmaps.use",
    "certificatemanager.certs.create",
    "certificatemanager.certs.delete",
    "certificatemanager.certs.get",
    "certificatemanager.certs.getIamPolicy",
    "certificatemanager.certs.list",
    "certificatemanager.certs.setIamPolicy",
    "certificatemanager.certs.update",
    "certificatemanager.certs.use",
    "certificatemanager.dnsauthorizations.create",
    "certificatemanager.dnsauthorizations.delete",
    "certificatemanager.dnsauthorizations.get",
    "certificatemanager.dnsauthorizations.getIamPolicy",
    "certificatemanager.dnsauthorizations.list",
    "certificatemanager.dnsauthorizations.setIamPolicy",
    "certificatemanager.dnsauthorizations.update",
    "certificatemanager.dnsauthorizations.use",
    "certificatemanager.locations.get",
    "certificatemanager.locations.list",
    "certificatemanager.operations.cancel",
    "certificatemanager.operations.delete",
    "certificatemanager.operations.get",
    "certificatemanager.operations.list",
    "chat.bots.get",
    "chat.bots.update",
    "chronicle.dashboards.copy",
    "chronicle.dashboards.create",
    "chronicle.dashboards.delete",
    "chronicle.dashboards.get",
    "chronicle.dashboards.list",
    "chronicle.multitenantDirectories.get",
    "clientauthconfig.brands.create",
    "clientauthconfig.brands.delete",
    "clientauthconfig.brands.get",
    "clientauthconfig.brands.list",
    "clientauthconfig.brands.update",
    "clientauthconfig.clients.create",
    "clientauthconfig.clients.createSecret",
    "clientauthconfig.clients.delete",
    "clientauthconfig.clients.get",
    "clientauthconfig.clients.getWithSecret",
    "clientauthconfig.clients.list",
    "clientauthconfig.clients.listWithSecrets",
    "clientauthconfig.clients.undelete",
    "clientauthconfig.clients.update",
    "cloudasset.assets.analyzeIamPolicy",
    "cloudasset.assets.analyzeMove",
    "cloudasset.assets.exportAccessLevel",
    "cloudasset.assets.exportAccessPolicy",
    "cloudasset.assets.exportAiplatformBatchPredictionJobs",
    "cloudasset.assets.exportAiplatformCustomJobs",
    "cloudasset.assets.exportAiplatformDataLabelingJobs",
    "cloudasset.assets.exportAiplatformDatasets",
    "cloudasset.assets.exportAiplatformEndpoints",
    "cloudasset.assets.exportAiplatformHyperparameterTuningJobs",
    "cloudasset.assets.exportAiplatformMetadataStores",
    "cloudasset.assets.exportAiplatformModelDeploymentMonitoringJobs",
    "cloudasset.assets.exportAiplatformModels",
    "cloudasset.assets.exportAiplatformPipelineJobs",
    "cloudasset.assets.exportAiplatformSpecialistPools",
    "cloudasset.assets.exportAiplatformTrainingPipelines",
    "cloudasset.assets.exportAllAccessPolicy",
    "cloudasset.assets.exportAnthosConnectedCluster",
    "cloudasset.assets.exportAnthosedgeCluster",
    "cloudasset.assets.exportApigatewayApi",
    "cloudasset.assets.exportApigatewayApiConfig",
    "cloudasset.assets.exportApigatewayGateway",
    "cloudasset.assets.exportApikeysKeys",
    "cloudasset.assets.exportAppengineApplications",
    "cloudasset.assets.exportAppengineServices",
    "cloudasset.assets.exportAppengineVersions",
    "cloudasset.assets.exportArtifactregistryDockerImages",
    "cloudasset.assets.exportArtifactregistryRepositories",
    "cloudasset.assets.exportAssuredWorkloadsWorkloads",
    "cloudasset.assets.exportBeyondCorpApiGateways",
    "cloudasset.assets.exportBeyondCorpAppConnections",
    "cloudasset.assets.exportBeyondCorpAppConnectors",
    "cloudasset.assets.exportBeyondCorpClientConnectorServices",
    "cloudasset.assets.exportBeyondCorpClientGateways",
    "cloudasset.assets.exportBigqueryDatasets",
    "cloudasset.assets.exportBigqueryModels",
    "cloudasset.assets.exportBigqueryTables",
    "cloudasset.assets.exportBigtableAppProfile",
    "cloudasset.assets.exportBigtableBackup",
    "cloudasset.assets.exportBigtableCluster",
    "cloudasset.assets.exportBigtableInstance",
    "cloudasset.assets.exportBigtableTable",
    "cloudasset.assets.exportCloudAssetFeeds",
    "cloudasset.assets.exportCloudDeployDeliveryPipelines",
    "cloudasset.assets.exportCloudDeployReleases",
    "cloudasset.assets.exportCloudDeployRollouts",
    "cloudasset.assets.exportCloudDeployTargets",
    "cloudasset.assets.exportCloudDocumentAIEvaluation",
    "cloudasset.assets.exportCloudDocumentAIHumanReviewConfig",
    "cloudasset.assets.exportCloudDocumentAILabelerPool",
    "cloudasset.assets.exportCloudDocumentAIProcessor",
    "cloudasset.assets.exportCloudDocumentAIProcessorVersion",
    "cloudasset.assets.exportCloudbillingBillingAccounts",
    "cloudasset.assets.exportCloudbillingProjectBillingInfos",
    "cloudasset.assets.exportCloudfunctionsFunctions",
    "cloudasset.assets.exportCloudfunctionsGen2Functions",
    "cloudasset.assets.exportCloudkmsCryptoKeyVersions",
    "cloudasset.assets.exportCloudkmsCryptoKeys",
    "cloudasset.assets.exportCloudkmsEkmConnections",
    "cloudasset.assets.exportCloudkmsImportJobs",
    "cloudasset.assets.exportCloudkmsKeyRings",
    "cloudasset.assets.exportCloudmemcacheInstances",
    "cloudasset.assets.exportCloudresourcemanagerFolders",
    "cloudasset.assets.exportCloudresourcemanagerOrganizations",
    "cloudasset.assets.exportCloudresourcemanagerProjects",
    "cloudasset.assets.exportCloudresourcemanagerTagBindings",
    "cloudasset.assets.exportCloudresourcemanagerTagKeys",
    "cloudasset.assets.exportCloudresourcemanagerTagValues",
    "cloudasset.assets.exportComposerEnvironments",
    "cloudasset.assets.exportComputeAddress",
    "cloudasset.assets.exportComputeAutoscalers",
    "cloudasset.assets.exportComputeBackendBuckets",
    "cloudasset.assets.exportComputeBackendServices",
    "cloudasset.assets.exportComputeCommitments",
    "cloudasset.assets.exportComputeDisks",
    "cloudasset.assets.exportComputeExternalVpnGateways",
    "cloudasset.assets.exportComputeFirewallPolicies",
    "cloudasset.assets.exportComputeFirewalls",
    "cloudasset.assets.exportComputeForwardingRules",
    "cloudasset.assets.exportComputeGlobalAddress",
    "cloudasset.assets.exportComputeGlobalForwardingRules",
    "cloudasset.assets.exportComputeHealthChecks",
    "cloudasset.assets.exportComputeHttpHealthChecks",
    "cloudasset.assets.exportComputeHttpsHealthChecks",
    "cloudasset.assets.exportComputeImages",
    "cloudasset.assets.exportComputeInstanceGroupManagers",
    "cloudasset.assets.exportComputeInstanceGroups",
    "cloudasset.assets.exportComputeInstanceTemplates",
    "cloudasset.assets.exportComputeInstances",
    "cloudasset.assets.exportComputeInterconnect",
    "cloudasset.assets.exportComputeInterconnectAttachment",
    "cloudasset.assets.exportComputeLicenses",
    "cloudasset.assets.exportComputeNetworkEndpointGroups",
    "cloudasset.assets.exportComputeNetworks",
    "cloudasset.assets.exportComputeNodeGroups",
    "cloudasset.assets.exportComputeNodeTemplates",
    "cloudasset.assets.exportComputePacketMirrorings",
    "cloudasset.assets.exportComputeProjects",
    "cloudasset.assets.exportComputeRegionAutoscaler",
    "cloudasset.assets.exportComputeRegionBackendServices",
    "cloudasset.assets.exportComputeRegionDisk",
    "cloudasset.assets.exportComputeRegionInstanceGroup",
    "cloudasset.assets.exportComputeRegionInstanceGroupManager",
    "cloudasset.assets.exportComputeReservations",
    "cloudasset.assets.exportComputeResourcePolicies",
    "cloudasset.assets.exportComputeRouters",
    "cloudasset.assets.exportComputeRoutes",
    "cloudasset.assets.exportComputeSecurityPolicy",
    "cloudasset.assets.exportComputeServiceAttachments",
    "cloudasset.assets.exportComputeSnapshots",
    "cloudasset.assets.exportComputeSslCertificates",
    "cloudasset.assets.exportComputeSslPolicies",
    "cloudasset.assets.exportComputeSubnetworks",
    "cloudasset.assets.exportComputeTargetHttpProxies",
    "cloudasset.assets.exportComputeTargetHttpsProxies",
    "cloudasset.assets.exportComputeTargetInstances",
    "cloudasset.assets.exportComputeTargetPools",
    "cloudasset.assets.exportComputeTargetSslProxies",
    "cloudasset.assets.exportComputeTargetTcpProxies",
    "cloudasset.assets.exportComputeTargetVpnGateways",
    "cloudasset.assets.exportComputeUrlMaps",
    "cloudasset.assets.exportComputeVpnGateways",
    "cloudasset.assets.exportComputeVpnTunnels",
    "cloudasset.assets.exportConnectorsConnections",
    "cloudasset.assets.exportConnectorsConnectorVersions",
    "cloudasset.assets.exportConnectorsConnectors",
    "cloudasset.assets.exportConnectorsProviders",
    "cloudasset.assets.exportConnectorsRuntimeConfigs",
    "cloudasset.assets.exportContainerAppsDeployment",
    "cloudasset.assets.exportContainerAppsReplicaSets",
    "cloudasset.assets.exportContainerBatchJobs",
    "cloudasset.assets.exportContainerClusterrole",
    "cloudasset.assets.exportContainerClusterrolebinding",
    "cloudasset.assets.exportContainerClusters",
    "cloudasset.assets.exportContainerExtensionsIngresses",
    "cloudasset.assets.exportContainerJobs",
    "cloudasset.assets.exportContainerNamespace",
    "cloudasset.assets.exportContainerNetworkingIngresses",
    "cloudasset.assets.exportContainerNetworkingNetworkPolicies",
    "cloudasset.assets.exportContainerNode",
    "cloudasset.assets.exportContainerNodepool",
    "cloudasset.assets.exportContainerPod",
    "cloudasset.assets.exportContainerReplicaSets",
    "cloudasset.assets.exportContainerRole",
    "cloudasset.assets.exportContainerRolebinding",
    "cloudasset.assets.exportContainerServices",
    "cloudasset.assets.exportContainerregistryImage",
    "cloudasset.assets.exportDataMigrationConnectionProfiles",
    "cloudasset.assets.exportDataMigrationMigrationJobs",
    "cloudasset.assets.exportDataflowJobs",
    "cloudasset.assets.exportDatafusionInstance",
    "cloudasset.assets.exportDataplexAssets",
    "cloudasset.assets.exportDataplexLakes",
    "cloudasset.assets.exportDataplexTasks",
    "cloudasset.assets.exportDataplexZones",
    "cloudasset.assets.exportDataprocAutoscalingPolicies",
    "cloudasset.assets.exportDataprocBatches",
    "cloudasset.assets.exportDataprocClusters",
    "cloudasset.assets.exportDataprocJobs",
    "cloudasset.assets.exportDataprocSessions",
    "cloudasset.assets.exportDataprocWorkflowTemplates",
    "cloudasset.assets.exportDatastreamConnectionProfile",
    "cloudasset.assets.exportDatastreamPrivateConnection",
    "cloudasset.assets.exportDatastreamStream",
    "cloudasset.assets.exportDialogflowAgents",
    "cloudasset.assets.exportDialogflowConversationProfiles",
    "cloudasset.assets.exportDialogflowKnowledgeBases",
    "cloudasset.assets.exportDialogflowLocationSettings",
    "cloudasset.assets.exportDlpDeidentifyTemplates",
    "cloudasset.assets.exportDlpDlpJobs",
    "cloudasset.assets.exportDlpInspectTemplates",
    "cloudasset.assets.exportDlpJobTriggers",
    "cloudasset.assets.exportDlpStoredInfoTypes",
    "cloudasset.assets.exportDnsManagedZones",
    "cloudasset.assets.exportDnsPolicies",
    "cloudasset.assets.exportDomainsRegistrations",
    "cloudasset.assets.exportEventarcTriggers",
    "cloudasset.assets.exportFileBackups",
    "cloudasset.assets.exportFileInstances",
    "cloudasset.assets.exportFirebaseAppInfos",
    "cloudasset.assets.exportFirebaseProjects",
    "cloudasset.assets.exportFirestoreDatabases",
    "cloudasset.assets.exportGKEHubFeatures",
    "cloudasset.assets.exportGKEHubMemberships",
    "cloudasset.assets.exportGameservicesGameServerClusters",
    "cloudasset.assets.exportGameservicesGameServerConfigs",
    "cloudasset.assets.exportGameservicesGameServerDeployments",
    "cloudasset.assets.exportGameservicesRealms",
    "cloudasset.assets.exportGkeBackupBackupPlans",
    "cloudasset.assets.exportGkeBackupBackups",
    "cloudasset.assets.exportGkeBackupRestorePlans",
    "cloudasset.assets.exportGkeBackupRestores",
    "cloudasset.assets.exportGkeBackupVolumeBackups",
    "cloudasset.assets.exportGkeBackupVolumeRestores",
    "cloudasset.assets.exportHealthcareConsentStores",
    "cloudasset.assets.exportHealthcareDatasets",
    "cloudasset.assets.exportHealthcareDicomStores",
    "cloudasset.assets.exportHealthcareFhirStores",
    "cloudasset.assets.exportHealthcareHl7V2Stores",
    "cloudasset.assets.exportIamPolicy",
    "cloudasset.assets.exportIamRoles",
    "cloudasset.assets.exportIamServiceAccountKeys",
    "cloudasset.assets.exportIamServiceAccounts",
    "cloudasset.assets.exportIapTunnel",
    "cloudasset.assets.exportIapTunnelInstances",
    "cloudasset.assets.exportIapTunnelZones",
    "cloudasset.assets.exportIapWeb",
    "cloudasset.assets.exportIapWebServiceVersion",
    "cloudasset.assets.exportIapWebServices",
    "cloudasset.assets.exportIapWebType",
    "cloudasset.assets.exportIdsEndpoints",
    "cloudasset.assets.exportIntegrationsAuthConfigs",
    "cloudasset.assets.exportIntegrationsCertificates",
    "cloudasset.assets.exportIntegrationsExecutions",
    "cloudasset.assets.exportIntegrationsIntegrationVersions",
    "cloudasset.assets.exportIntegrationsIntegrations",
    "cloudasset.assets.exportIntegrationsSfdcChannels",
    "cloudasset.assets.exportIntegrationsSfdcInstances",
    "cloudasset.assets.exportIntegrationsSuspensions",
    "cloudasset.assets.exportLoggingLogMetrics",
    "cloudasset.assets.exportLoggingLogSinks",
    "cloudasset.assets.exportManagedidentitiesDomain",
    "cloudasset.assets.exportMetastoreBackups",
    "cloudasset.assets.exportMetastoreMetadataImports",
    "cloudasset.assets.exportMetastoreServices",
    "cloudasset.assets.exportMonitoringAlertPolicies",
    "cloudasset.assets.exportNetworkConnectivityHubs",
    "cloudasset.assets.exportNetworkConnectivitySpokes",
    "cloudasset.assets.exportNetworkManagementConnectivityTests",
    "cloudasset.assets.exportNetworkServicesEndpointPolicies",
    "cloudasset.assets.exportNetworkServicesGateways",
    "cloudasset.assets.exportNetworkServicesGrpcRoutes",
    "cloudasset.assets.exportNetworkServicesHttpRoutes",
    "cloudasset.assets.exportNetworkServicesMeshes",
    "cloudasset.assets.exportNetworkServicesServiceBindings",
    "cloudasset.assets.exportNetworkServicesTcpRoutes",
    "cloudasset.assets.exportNetworkServicesTlsRoutes",
    "cloudasset.assets.exportOSConfigOSPolicyAssignmentReports",
    "cloudasset.assets.exportOSConfigOSPolicyAssignments",
    "cloudasset.assets.exportOSConfigVulnerabilityReports",
    "cloudasset.assets.exportOSInventories",
    "cloudasset.assets.exportOrgPolicy",
    "cloudasset.assets.exportPatchDeployments",
    "cloudasset.assets.exportPubsubSnapshots",
    "cloudasset.assets.exportPubsubSubscriptions",
    "cloudasset.assets.exportPubsubTopics",
    "cloudasset.assets.exportRedisInstances",
    "cloudasset.assets.exportResource",
    "cloudasset.assets.exportSecretManagerSecretVersions",
    "cloudasset.assets.exportSecretManagerSecrets",
    "cloudasset.assets.exportServiceDirectoryNamespaces",
    "cloudasset.assets.exportServicePerimeter",
    "cloudasset.assets.exportServiceconsumermanagementConsumerProperty",
    "cloudasset.assets.exportServiceconsumermanagementConsumerQuotaLimits",
    "cloudasset.assets.exportServiceconsumermanagementConsumers",
    "cloudasset.assets.exportServiceconsumermanagementProducerOverrides",
    "cloudasset.assets.exportServiceconsumermanagementTenancyUnits",
    "cloudasset.assets.exportServiceconsumermanagementVisibility",
    "cloudasset.assets.exportServicemanagementServices",
    "cloudasset.assets.exportServiceusageAdminOverrides",
    "cloudasset.assets.exportServiceusageConsumerOverrides",
    "cloudasset.assets.exportServiceusageServices",
    "cloudasset.assets.exportSpannerBackups",
    "cloudasset.assets.exportSpannerDatabases",
    "cloudasset.assets.exportSpannerInstances",
    "cloudasset.assets.exportSpeakerIdPhrases",
    "cloudasset.assets.exportSpeakerIdSettings",
    "cloudasset.assets.exportSpeakerIdSpeakers",
    "cloudasset.assets.exportSpeechCustomClasses",
    "cloudasset.assets.exportSpeechPhraseSets",
    "cloudasset.assets.exportSqladminBackupRuns",
    "cloudasset.assets.exportSqladminInstances",
    "cloudasset.assets.exportStorageBuckets",
    "cloudasset.assets.exportTpuNodes",
    "cloudasset.assets.exportVpcaccessConnector",
    "cloudasset.assets.listAccessLevel",
    "cloudasset.assets.listAccessPolicy",
    "cloudasset.assets.listAiplatformBatchPredictionJobs",
    "cloudasset.assets.listAiplatformCustomJobs",
    "cloudasset.assets.listAiplatformDataLabelingJobs",
    "cloudasset.assets.listAiplatformDatasets",
    "cloudasset.assets.listAiplatformEndpoints",
    "cloudasset.assets.listAiplatformHyperparameterTuningJobs",
    "cloudasset.assets.listAiplatformMetadataStores",
    "cloudasset.assets.listAiplatformModelDeploymentMonitoringJobs",
    "cloudasset.assets.listAiplatformModels",
    "cloudasset.assets.listAiplatformPipelineJobs",
    "cloudasset.assets.listAiplatformSpecialistPools",
    "cloudasset.assets.listAiplatformTrainingPipelines",
    "cloudasset.assets.listAllAccessPolicy",
    "cloudasset.assets.listAnthosConnectedCluster",
    "cloudasset.assets.listAnthosedgeCluster",
    "cloudasset.assets.listApigatewayApi",
    "cloudasset.assets.listApigatewayApiConfig",
    "cloudasset.assets.listApigatewayGateway",
    "cloudasset.assets.listApikeysKeys",
    "cloudasset.assets.listAppengineApplications",
    "cloudasset.assets.listAppengineServices",
    "cloudasset.assets.listAppengineVersions",
    "cloudasset.assets.listArtifactregistryDockerImages",
    "cloudasset.assets.listArtifactregistryRepositories",
    "cloudasset.assets.listAssuredWorkloadsWorkloads",
    "cloudasset.assets.listBeyondCorpApiGateways",
    "cloudasset.assets.listBeyondCorpAppConnections",
    "cloudasset.assets.listBeyondCorpAppConnectors",
    "cloudasset.assets.listBeyondCorpClientConnectorServices",
    "cloudasset.assets.listBeyondCorpClientGateways",
    "cloudasset.assets.listBigqueryDatasets",
    "cloudasset.assets.listBigqueryModels",
    "cloudasset.assets.listBigqueryTables",
    "cloudasset.assets.listBigtableAppProfile",
    "cloudasset.assets.listBigtableBackup",
    "cloudasset.assets.listBigtableCluster",
    "cloudasset.assets.listBigtableInstance",
    "cloudasset.assets.listBigtableTable",
    "cloudasset.assets.listCloudAssetFeeds",
    "cloudasset.assets.listCloudDeployDeliveryPipelines",
    "cloudasset.assets.listCloudDeployReleases",
    "cloudasset.assets.listCloudDeployRollouts",
    "cloudasset.assets.listCloudDeployTargets",
    "cloudasset.assets.listCloudDocumentAIEvaluation",
    "cloudasset.assets.listCloudDocumentAIHumanReviewConfig",
    "cloudasset.assets.listCloudDocumentAILabelerPool",
    "cloudasset.assets.listCloudDocumentAIProcessor",
    "cloudasset.assets.listCloudDocumentAIProcessorVersion",
    "cloudasset.assets.listCloudbillingBillingAccounts",
    "cloudasset.assets.listCloudbillingProjectBillingInfos",
    "cloudasset.assets.listCloudfunctionsFunctions",
    "cloudasset.assets.listCloudfunctionsGen2Functions",
    "cloudasset.assets.listCloudkmsCryptoKeyVersions",
    "cloudasset.assets.listCloudkmsCryptoKeys",
    "cloudasset.assets.listCloudkmsEkmConnections",
    "cloudasset.assets.listCloudkmsImportJobs",
    "cloudasset.assets.listCloudkmsKeyRings",
    "cloudasset.assets.listCloudmemcacheInstances",
    "cloudasset.assets.listCloudresourcemanagerFolders",
    "cloudasset.assets.listCloudresourcemanagerOrganizations",
    "cloudasset.assets.listCloudresourcemanagerProjects",
    "cloudasset.assets.listCloudresourcemanagerTagBindings",
    "cloudasset.assets.listCloudresourcemanagerTagKeys",
    "cloudasset.assets.listCloudresourcemanagerTagValues",
    "cloudasset.assets.listComposerEnvironments",
    "cloudasset.assets.listComputeAddress",
    "cloudasset.assets.listComputeAutoscalers",
    "cloudasset.assets.listComputeBackendBuckets",
    "cloudasset.assets.listComputeBackendServices",
    "cloudasset.assets.listComputeCommitments",
    "cloudasset.assets.listComputeDisks",
    "cloudasset.assets.listComputeExternalVpnGateways",
    "cloudasset.assets.listComputeFirewallPolicies",
    "cloudasset.assets.listComputeFirewalls",
    "cloudasset.assets.listComputeForwardingRules",
    "cloudasset.assets.listComputeGlobalAddress",
    "cloudasset.assets.listComputeGlobalForwardingRules",
    "cloudasset.assets.listComputeHealthChecks",
    "cloudasset.assets.listComputeHttpHealthChecks",
    "cloudasset.assets.listComputeHttpsHealthChecks",
    "cloudasset.assets.listComputeImages",
    "cloudasset.assets.listComputeInstanceGroupManagers",
    "cloudasset.assets.listComputeInstanceGroups",
    "cloudasset.assets.listComputeInstanceTemplates",
    "cloudasset.assets.listComputeInstances",
    "cloudasset.assets.listComputeInterconnect",
    "cloudasset.assets.listComputeInterconnectAttachment",
    "cloudasset.assets.listComputeLicenses",
    "cloudasset.assets.listComputeNetworkEndpointGroups",
    "cloudasset.assets.listComputeNetworks",
    "cloudasset.assets.listComputeNodeGroups",
    "cloudasset.assets.listComputeNodeTemplates",
    "cloudasset.assets.listComputePacketMirrorings",
    "cloudasset.assets.listComputeProjects",
    "cloudasset.assets.listComputeRegionAutoscaler",
    "cloudasset.assets.listComputeRegionBackendServices",
    "cloudasset.assets.listComputeRegionDisk",
    "cloudasset.assets.listComputeRegionInstanceGroup",
    "cloudasset.assets.listComputeRegionInstanceGroupManager",
    "cloudasset.assets.listComputeReservations",
    "cloudasset.assets.listComputeResourcePolicies",
    "cloudasset.assets.listComputeRouters",
    "cloudasset.assets.listComputeRoutes",
    "cloudasset.assets.listComputeSecurityPolicy",
    "cloudasset.assets.listComputeServiceAttachments",
    "cloudasset.assets.listComputeSnapshots",
    "cloudasset.assets.listComputeSslCertificates",
    "cloudasset.assets.listComputeSslPolicies",
    "cloudasset.assets.listComputeSubnetworks",
    "cloudasset.assets.listComputeTargetHttpProxies",
    "cloudasset.assets.listComputeTargetHttpsProxies",
    "cloudasset.assets.listComputeTargetInstances",
    "cloudasset.assets.listComputeTargetPools",
    "cloudasset.assets.listComputeTargetSslProxies",
    "cloudasset.assets.listComputeTargetTcpProxies",
    "cloudasset.assets.listComputeTargetVpnGateways",
    "cloudasset.assets.listComputeUrlMaps",
    "cloudasset.assets.listComputeVpnGateways",
    "cloudasset.assets.listComputeVpnTunnels",
    "cloudasset.assets.listConnectorsConnections",
    "cloudasset.assets.listConnectorsConnectorVersions",
    "cloudasset.assets.listConnectorsConnectors",
    "cloudasset.assets.listConnectorsProviders",
    "cloudasset.assets.listConnectorsRuntimeConfigs",
    "cloudasset.assets.listContainerAppsDeployment",
    "cloudasset.assets.listContainerAppsReplicaSets",
    "cloudasset.assets.listContainerBatchJobs",
    "cloudasset.assets.listContainerClusterrole",
    "cloudasset.assets.listContainerClusterrolebinding",
    "cloudasset.assets.listContainerClusters",
    "cloudasset.assets.listContainerExtensionsIngresses",
    "cloudasset.assets.listContainerJobs",
    "cloudasset.assets.listContainerNamespace",
    "cloudasset.assets.listContainerNetworkingIngresses",
    "cloudasset.assets.listContainerNetworkingNetworkPolicies",
    "cloudasset.assets.listContainerNode",
    "cloudasset.assets.listContainerNodepool",
    "cloudasset.assets.listContainerPod",
    "cloudasset.assets.listContainerReplicaSets",
    "cloudasset.assets.listContainerRole",
    "cloudasset.assets.listContainerRolebinding",
    "cloudasset.assets.listContainerServices",
    "cloudasset.assets.listContainerregistryImage",
    "cloudasset.assets.listDataMigrationConnectionProfiles",
    "cloudasset.assets.listDataMigrationMigrationJobs",
    "cloudasset.assets.listDataflowJobs",
    "cloudasset.assets.listDatafusionInstance",
    "cloudasset.assets.listDataplexAssets",
    "cloudasset.assets.listDataplexLakes",
    "cloudasset.assets.listDataplexTasks",
    "cloudasset.assets.listDataplexZones",
    "cloudasset.assets.listDataprocAutoscalingPolicies",
    "cloudasset.assets.listDataprocBatches",
    "cloudasset.assets.listDataprocClusters",
    "cloudasset.assets.listDataprocJobs",
    "cloudasset.assets.listDataprocSessions",
    "cloudasset.assets.listDataprocWorkflowTemplates",
    "cloudasset.assets.listDatastreamConnectionProfile",
    "cloudasset.assets.listDatastreamPrivateConnection",
    "cloudasset.assets.listDatastreamStream",
    "cloudasset.assets.listDialogflowAgents",
    "cloudasset.assets.listDialogflowConversationProfiles",
    "cloudasset.assets.listDialogflowKnowledgeBases",
    "cloudasset.assets.listDialogflowLocationSettings",
    "cloudasset.assets.listDlpDeidentifyTemplates",
    "cloudasset.assets.listDlpDlpJobs",
    "cloudasset.assets.listDlpInspectTemplates",
    "cloudasset.assets.listDlpJobTriggers",
    "cloudasset.assets.listDlpStoredInfoTypes",
    "cloudasset.assets.listDnsManagedZones",
    "cloudasset.assets.listDnsPolicies",
    "cloudasset.assets.listDomainsRegistrations",
    "cloudasset.assets.listEventarcTriggers",
    "cloudasset.assets.listFileBackups",
    "cloudasset.assets.listFileInstances",
    "cloudasset.assets.listFirebaseAppInfos",
    "cloudasset.assets.listFirebaseProjects",
    "cloudasset.assets.listFirestoreDatabases",
    "cloudasset.assets.listGKEHubFeatures",
    "cloudasset.assets.listGKEHubMemberships",
    "cloudasset.assets.listGameservicesGameServerClusters",
    "cloudasset.assets.listGameservicesGameServerConfigs",
    "cloudasset.assets.listGameservicesGameServerDeployments",
    "cloudasset.assets.listGameservicesRealms",
    "cloudasset.assets.listGkeBackupBackupPlans",
    "cloudasset.assets.listGkeBackupBackups",
    "cloudasset.assets.listGkeBackupRestorePlans",
    "cloudasset.assets.listGkeBackupRestores",
    "cloudasset.assets.listGkeBackupVolumeBackups",
    "cloudasset.assets.listGkeBackupVolumeRestores",
    "cloudasset.assets.listHealthcareConsentStores",
    "cloudasset.assets.listHealthcareDatasets",
    "cloudasset.assets.listHealthcareDicomStores",
    "cloudasset.assets.listHealthcareFhirStores",
    "cloudasset.assets.listHealthcareHl7V2Stores",
    "cloudasset.assets.listIamPolicy",
    "cloudasset.assets.listIamRoles",
    "cloudasset.assets.listIamServiceAccountKeys",
    "cloudasset.assets.listIamServiceAccounts",
    "cloudasset.assets.listIapTunnel",
    "cloudasset.assets.listIapTunnelInstances",
    "cloudasset.assets.listIapTunnelZones",
    "cloudasset.assets.listIapWeb",
    "cloudasset.assets.listIapWebServiceVersion",
    "cloudasset.assets.listIapWebServices",
    "cloudasset.assets.listIapWebType",
    "cloudasset.assets.listIdsEndpoints",
    "cloudasset.assets.listIntegrationsAuthConfigs",
    "cloudasset.assets.listIntegrationsCertificates",
    "cloudasset.assets.listIntegrationsExecutions",
    "cloudasset.assets.listIntegrationsIntegrationVersions",
    "cloudasset.assets.listIntegrationsIntegrations",
    "cloudasset.assets.listIntegrationsSfdcChannels",
    "cloudasset.assets.listIntegrationsSfdcInstances",
    "cloudasset.assets.listIntegrationsSuspensions",
    "cloudasset.assets.listLoggingLogMetrics",
    "cloudasset.assets.listLoggingLogSinks",
    "cloudasset.assets.listManagedidentitiesDomain",
    "cloudasset.assets.listMetastoreBackups",
    "cloudasset.assets.listMetastoreMetadataImports",
    "cloudasset.assets.listMetastoreServices",
    "cloudasset.assets.listMonitoringAlertPolicies",
    "cloudasset.assets.listNetworkConnectivityHubs",
    "cloudasset.assets.listNetworkConnectivitySpokes",
    "cloudasset.assets.listNetworkManagementConnectivityTests",
    "cloudasset.assets.listNetworkServicesEndpointPolicies",
    "cloudasset.assets.listNetworkServicesGateways",
    "cloudasset.assets.listNetworkServicesGrpcRoutes",
    "cloudasset.assets.listNetworkServicesHttpRoutes",
    "cloudasset.assets.listNetworkServicesMeshes",
    "cloudasset.assets.listNetworkServicesServiceBindings",
    "cloudasset.assets.listNetworkServicesTcpRoutes",
    "cloudasset.assets.listNetworkServicesTlsRoutes",
    "cloudasset.assets.listOSConfigOSPolicyAssignmentReports",
    "cloudasset.assets.listOSConfigOSPolicyAssignments",
    "cloudasset.assets.listOSConfigVulnerabilityReports",
    "cloudasset.assets.listOSInventories",
    "cloudasset.assets.listOrgPolicy",
    "cloudasset.assets.listPatchDeployments",
    "cloudasset.assets.listPubsubSnapshots",
    "cloudasset.assets.listPubsubSubscriptions",
    "cloudasset.assets.listPubsubTopics",
    "cloudasset.assets.listRedisInstances",
    "cloudasset.assets.listResource",
    "cloudasset.assets.listRunDomainMapping",
    "cloudasset.assets.listRunRevision",
    "cloudasset.assets.listRunService",
    "cloudasset.assets.listSecretManagerSecretVersions",
    "cloudasset.assets.listSecretManagerSecrets",
    "cloudasset.assets.listServiceDirectoryNamespaces",
    "cloudasset.assets.listServicePerimeter",
    "cloudasset.assets.listServiceconsumermanagementConsumerProperty",
    "cloudasset.assets.listServiceconsumermanagementConsumerQuotaLimits",
    "cloudasset.assets.listServiceconsumermanagementConsumers",
    "cloudasset.assets.listServiceconsumermanagementProducerOverrides",
    "cloudasset.assets.listServiceconsumermanagementTenancyUnits",
    "cloudasset.assets.listServiceconsumermanagementVisibility",
    "cloudasset.assets.listServicemanagementServices",
    "cloudasset.assets.listServiceusageAdminOverrides",
    "cloudasset.assets.listServiceusageConsumerOverrides",
    "cloudasset.assets.listServiceusageServices",
    "cloudasset.assets.listSpannerBackups",
    "cloudasset.assets.listSpannerDatabases",
    "cloudasset.assets.listSpannerInstances",
    "cloudasset.assets.listSpeakerIdPhrases",
    "cloudasset.assets.listSpeakerIdSettings",
    "cloudasset.assets.listSpeakerIdSpeakers",
    "cloudasset.assets.listSpeechCustomClasses",
    "cloudasset.assets.listSpeechPhraseSets",
    "cloudasset.assets.listSqladminBackupRuns",
    "cloudasset.assets.listSqladminInstances",
    "cloudasset.assets.listStorageBuckets",
    "cloudasset.assets.listTpuNodes",
    "cloudasset.assets.listVpcaccessConnector",
    "cloudasset.assets.searchAllIamPolicies",
    "cloudasset.assets.searchAllResources",
    "cloudasset.feeds.create",
    "cloudasset.feeds.delete",
    "cloudasset.feeds.get",
    "cloudasset.feeds.list",
    "cloudasset.feeds.update",
    "cloudasset.savedqueries.create",
    "cloudasset.savedqueries.delete",
    "cloudasset.savedqueries.get",
    "cloudasset.savedqueries.list",
    "cloudasset.savedqueries.update",
    "cloudbuild.builds.approve",
    "cloudbuild.builds.create",
    "cloudbuild.builds.get",
    "cloudbuild.builds.list",
    "cloudbuild.builds.update",
    "cloudbuild.integrations.create",
    "cloudbuild.integrations.delete",
    "cloudbuild.integrations.get",
    "cloudbuild.integrations.list",
    "cloudbuild.integrations.update",
    "cloudbuild.workerpools.create",
    "cloudbuild.workerpools.delete",
    "cloudbuild.workerpools.get",
    "cloudbuild.workerpools.list",
    "cloudbuild.workerpools.update",
    "cloudbuild.workerpools.use",
    "cloudconfig.configs.get",
    "cloudconfig.configs.update",
    "clouddebugger.breakpoints.create",
    "clouddebugger.breakpoints.delete",
    "clouddebugger.breakpoints.get",
    "clouddebugger.breakpoints.list",
    "clouddebugger.breakpoints.listActive",
    "clouddebugger.breakpoints.update",
    "clouddebugger.debuggees.create",
    "clouddebugger.debuggees.list",
    "clouddeploy.config.get",
    "clouddeploy.deliveryPipelines.create",
    "clouddeploy.deliveryPipelines.delete",
    "clouddeploy.deliveryPipelines.get",
    "clouddeploy.deliveryPipelines.getIamPolicy",
    "clouddeploy.deliveryPipelines.list",
    "clouddeploy.deliveryPipelines.setIamPolicy",
    "clouddeploy.deliveryPipelines.update",
    "clouddeploy.jobRuns.get",
    "clouddeploy.jobRuns.list",
    "clouddeploy.locations.get",
    "clouddeploy.locations.list",
    "clouddeploy.operations.cancel",
    "clouddeploy.operations.delete",
    "clouddeploy.operations.get",
    "clouddeploy.operations.list",
    "clouddeploy.releases.abandon",
    "clouddeploy.releases.create",
    "clouddeploy.releases.delete",
    "clouddeploy.releases.get",
    "clouddeploy.releases.list",
    "clouddeploy.rollouts.approve",
    "clouddeploy.rollouts.create",
    "clouddeploy.rollouts.get",
    "clouddeploy.rollouts.list",
    "clouddeploy.rollouts.retryJob",
    "clouddeploy.targets.create",
    "clouddeploy.targets.delete",
    "clouddeploy.targets.get",
    "clouddeploy.targets.getIamPolicy",
    "clouddeploy.targets.list",
    "clouddeploy.targets.setIamPolicy",
    "clouddeploy.targets.update",
    "cloudfunctions.functions.call",
    "cloudfunctions.functions.create",
    "cloudfunctions.functions.delete",
    "cloudfunctions.functions.get",
    "cloudfunctions.functions.getIamPolicy",
    "cloudfunctions.functions.invoke",
    "cloudfunctions.functions.list",
    "cloudfunctions.functions.setIamPolicy",
    "cloudfunctions.functions.sourceCodeGet",
    "cloudfunctions.functions.sourceCodeSet",
    "cloudfunctions.functions.update",
    "cloudfunctions.locations.get",
    "cloudfunctions.locations.list",
    "cloudfunctions.operations.get",
    "cloudfunctions.operations.list",
    "cloudfunctions.runtimes.list",
    "cloudiot.devices.bindGateway",
    "cloudiot.devices.create",
    "cloudiot.devices.delete",
    "cloudiot.devices.get",
    "cloudiot.devices.list",
    "cloudiot.devices.sendCommand",
    "cloudiot.devices.unbindGateway",
    "cloudiot.devices.update",
    "cloudiot.devices.updateConfig",
    "cloudiot.registries.create",
    "cloudiot.registries.delete",
    "cloudiot.registries.get",
    "cloudiot.registries.getIamPolicy",
    "cloudiot.registries.list",
    "cloudiot.registries.setIamPolicy",
    "cloudiot.registries.update",
    "cloudiottoken.tokensettings.get",
    "cloudiottoken.tokensettings.update",
    "cloudjobdiscovery.companies.create",
    "cloudjobdiscovery.companies.delete",
    "cloudjobdiscovery.companies.get",
    "cloudjobdiscovery.companies.list",
    "cloudjobdiscovery.companies.update",
    "cloudjobdiscovery.events.create",
    "cloudjobdiscovery.jobs.create",
    "cloudjobdiscovery.jobs.delete",
    "cloudjobdiscovery.jobs.get",
    "cloudjobdiscovery.jobs.search",
    "cloudjobdiscovery.jobs.update",
    "cloudjobdiscovery.profiles.create",
    "cloudjobdiscovery.profiles.delete",
    "cloudjobdiscovery.profiles.get",
    "cloudjobdiscovery.profiles.search",
    "cloudjobdiscovery.profiles.update",
    "cloudjobdiscovery.tenants.create",
    "cloudjobdiscovery.tenants.delete",
    "cloudjobdiscovery.tenants.get",
    "cloudjobdiscovery.tenants.update",
    "cloudjobdiscovery.tools.access",
    "cloudkms.cryptoKeyVersions.create",
    "cloudkms.cryptoKeyVersions.destroy",
    "cloudkms.cryptoKeyVersions.get",
    "cloudkms.cryptoKeyVersions.list",
    "cloudkms.cryptoKeyVersions.manageRawPKCS1Keys",
    "cloudkms.cryptoKeyVersions.restore",
    "cloudkms.cryptoKeyVersions.update",
    "cloudkms.cryptoKeyVersions.useToDecrypt",
    "cloudkms.cryptoKeyVersions.useToDecryptViaDelegation",
    "cloudkms.cryptoKeyVersions.useToEncrypt",
    "cloudkms.cryptoKeyVersions.useToEncryptViaDelegation",
    "cloudkms.cryptoKeyVersions.useToSign",
    "cloudkms.cryptoKeyVersions.useToVerify",
    "cloudkms.cryptoKeyVersions.viewPublicKey",
    "cloudkms.cryptoKeys.create",
    "cloudkms.cryptoKeys.get",
    "cloudkms.cryptoKeys.getIamPolicy",
    "cloudkms.cryptoKeys.list",
    "cloudkms.cryptoKeys.setIamPolicy",
    "cloudkms.cryptoKeys.update",
    "cloudkms.ekmConnections.create",
    "cloudkms.ekmConnections.get",
    "cloudkms.ekmConnections.getIamPolicy",
    "cloudkms.ekmConnections.list",
    "cloudkms.ekmConnections.setIamPolicy",
    "cloudkms.ekmConnections.update",
    "cloudkms.ekmConnections.use",
    "cloudkms.importJobs.create",
    "cloudkms.importJobs.get",
    "cloudkms.importJobs.getIamPolicy",
    "cloudkms.importJobs.list",
    "cloudkms.importJobs.setIamPolicy",
    "cloudkms.importJobs.useToImport",
    "cloudkms.keyRings.create",
    "cloudkms.keyRings.createTagBinding",
    "cloudkms.keyRings.deleteTagBinding",
    "cloudkms.keyRings.get",
    "cloudkms.keyRings.getIamPolicy",
    "cloudkms.keyRings.list",
    "cloudkms.keyRings.listEffectiveTags",
    "cloudkms.keyRings.listTagBindings",
    "cloudkms.keyRings.setIamPolicy",
    "cloudkms.locations.generateRandomBytes",
    "cloudkms.locations.get",
    "cloudkms.locations.list",
    "cloudmessaging.messages.create",
    "cloudmigration.velostrataendpoints.connect",
    "cloudnotifications.activities.list",
    "cloudoptimization.operations.create",
    "cloudoptimization.operations.get",
    "cloudprivatecatalog.targets.get",
    "cloudprivatecatalogproducer.catalogAssociations.create",
    "cloudprivatecatalogproducer.catalogAssociations.delete",
    "cloudprivatecatalogproducer.catalogAssociations.get",
    "cloudprivatecatalogproducer.catalogAssociations.list",
    "cloudprivatecatalogproducer.producerCatalogs.attachProduct",
    "cloudprivatecatalogproducer.producerCatalogs.create",
    "cloudprivatecatalogproducer.producerCatalogs.delete",
    "cloudprivatecatalogproducer.producerCatalogs.detachProduct",
    "cloudprivatecatalogproducer.producerCatalogs.get",
    "cloudprivatecatalogproducer.producerCatalogs.getIamPolicy",
    "cloudprivatecatalogproducer.producerCatalogs.list",
    "cloudprivatecatalogproducer.producerCatalogs.setIamPolicy",
    "cloudprivatecatalogproducer.producerCatalogs.update",
    "cloudprivatecatalogproducer.products.create",
    "cloudprivatecatalogproducer.products.delete",
    "cloudprivatecatalogproducer.products.get",
    "cloudprivatecatalogproducer.products.getIamPolicy",
    "cloudprivatecatalogproducer.products.list",
    "cloudprivatecatalogproducer.products.setIamPolicy",
    "cloudprivatecatalogproducer.products.update",
    "cloudprivatecatalogproducer.targets.associate",
    "cloudprivatecatalogproducer.targets.unassociate",
    "cloudprofiler.profiles.create",
    "cloudprofiler.profiles.list",
    "cloudprofiler.profiles.update",
    "cloudscheduler.jobs.create",
    "cloudscheduler.jobs.delete",
    "cloudscheduler.jobs.enable",
    "cloudscheduler.jobs.fullView",
    "cloudscheduler.jobs.get",
    "cloudscheduler.jobs.list",
    "cloudscheduler.jobs.pause",
    "cloudscheduler.jobs.run",
    "cloudscheduler.jobs.update",
    "cloudscheduler.locations.get",
    "cloudscheduler.locations.list",
    "cloudsecurityscanner.crawledurls.list",
    "cloudsecurityscanner.results.get",
    "cloudsecurityscanner.results.list",
    "cloudsecurityscanner.scanruns.get",
    "cloudsecurityscanner.scanruns.getSummary",
    "cloudsecurityscanner.scanruns.list",
    "cloudsecurityscanner.scanruns.stop",
    "cloudsecurityscanner.scans.create",
    "cloudsecurityscanner.scans.delete",
    "cloudsecurityscanner.scans.get",
    "cloudsecurityscanner.scans.list",
    "cloudsecurityscanner.scans.run",
    "cloudsecurityscanner.scans.update",
    "cloudsql.backupRuns.create",
    "cloudsql.backupRuns.delete",
    "cloudsql.backupRuns.get",
    "cloudsql.backupRuns.list",
    "cloudsql.databases.create",
    "cloudsql.databases.delete",
    "cloudsql.databases.get",
    "cloudsql.databases.list",
    "cloudsql.databases.update",
    "cloudsql.instances.addServerCa",
    "cloudsql.instances.clone",
    "cloudsql.instances.connect",
    "cloudsql.instances.create",
    "cloudsql.instances.createTagBinding",
    "cloudsql.instances.delete",
    "cloudsql.instances.deleteTagBinding",
    "cloudsql.instances.demoteMaster",
    "cloudsql.instances.export",
    "cloudsql.instances.failover",
    "cloudsql.instances.get",
    "cloudsql.instances.import",
    "cloudsql.instances.list",
    "cloudsql.instances.listEffectiveTags",
    "cloudsql.instances.listServerCas",
    "cloudsql.instances.listTagBindings",
    "cloudsql.instances.login",
    "cloudsql.instances.promoteReplica",
    "cloudsql.instances.resetSslConfig",
    "cloudsql.instances.restart",
    "cloudsql.instances.restoreBackup",
    "cloudsql.instances.rotateServerCa",
    "cloudsql.instances.startReplica",
    "cloudsql.instances.stopReplica",
    "cloudsql.instances.truncateLog",
    "cloudsql.instances.update",
    "cloudsql.sslCerts.create",
    "cloudsql.sslCerts.createEphemeral",
    "cloudsql.sslCerts.delete",
    "cloudsql.sslCerts.get",
    "cloudsql.sslCerts.list",
    "cloudsql.users.create",
    "cloudsql.users.delete",
    "cloudsql.users.get",
    "cloudsql.users.list",
    "cloudsql.users.update",
    "cloudsupport.properties.get",
    "cloudsupport.techCases.create",
    "cloudsupport.techCases.escalate",
    "cloudsupport.techCases.get",
    "cloudsupport.techCases.list",
    "cloudsupport.techCases.update",
    "cloudtasks.locations.get",
    "cloudtasks.locations.list",
    "cloudtasks.queues.create",
    "cloudtasks.queues.delete",
    "cloudtasks.queues.get",
    "cloudtasks.queues.getIamPolicy",
    "cloudtasks.queues.list",
    "cloudtasks.queues.pause",
    "cloudtasks.queues.purge",
    "cloudtasks.queues.resume",
    "cloudtasks.queues.setIamPolicy",
    "cloudtasks.queues.update",
    "cloudtasks.tasks.create",
    "cloudtasks.tasks.delete",
    "cloudtasks.tasks.fullView",
    "cloudtasks.tasks.get",
    "cloudtasks.tasks.list",
    "cloudtasks.tasks.run",
    "cloudtestservice.environmentcatalog.get",
    "cloudtestservice.matrices.create",
    "cloudtestservice.matrices.get",
    "cloudtestservice.matrices.update",
    "cloudtoolresults.executions.create",
    "cloudtoolresults.executions.get",
    "cloudtoolresults.executions.list",
    "cloudtoolresults.executions.update",
    "cloudtoolresults.histories.create",
    "cloudtoolresults.histories.get",
    "cloudtoolresults.histories.list",
    "cloudtoolresults.settings.create",
    "cloudtoolresults.settings.get",
    "cloudtoolresults.settings.update",
    "cloudtoolresults.steps.create",
    "cloudtoolresults.steps.get",
    "cloudtoolresults.steps.list",
    "cloudtoolresults.steps.update",
    "cloudtrace.insights.get",
    "cloudtrace.insights.list",
    "cloudtrace.stats.get",
    "cloudtrace.tasks.create",
    "cloudtrace.tasks.delete",
    "cloudtrace.tasks.get",
    "cloudtrace.tasks.list",
    "cloudtrace.traces.get",
    "cloudtrace.traces.list",
    "cloudtrace.traces.patch",
    "cloudtranslate.generalModels.batchDocPredict",
    "cloudtranslate.generalModels.batchPredict",
    "cloudtranslate.generalModels.docPredict",
    "cloudtranslate.generalModels.get",
    "cloudtranslate.generalModels.predict",
    "cloudtranslate.glossaries.batchDocPredict",
    "cloudtranslate.glossaries.batchPredict",
    "cloudtranslate.glossaries.create",
    "cloudtranslate.glossaries.delete",
    "cloudtranslate.glossaries.docPredict",
    "cloudtranslate.glossaries.get",
    "cloudtranslate.glossaries.list",
    "cloudtranslate.glossaries.predict",
    "cloudtranslate.glossaries.update",
    "cloudtranslate.glossaryentries.create",
    "cloudtranslate.glossaryentries.delete",
    "cloudtranslate.glossaryentries.get",
    "cloudtranslate.glossaryentries.list",
    "cloudtranslate.glossaryentries.update",
    "cloudtranslate.languageDetectionModels.predict",
    "cloudtranslate.locations.get",
    "cloudtranslate.locations.list",
    "cloudtranslate.operations.cancel",
    "cloudtranslate.operations.delete",
    "cloudtranslate.operations.get",
    "cloudtranslate.operations.list",
    "cloudtranslate.operations.wait",
    "commercebusinessenablement.leadgenConfig.get",
    "commercebusinessenablement.leadgenConfig.update",
    "commercebusinessenablement.paymentConfig.get",
    "commercebusinessenablement.paymentConfig.update",
    "commerceorggovernance.consumerSharingPolicies.get",
    "commerceorggovernance.consumerSharingPolicies.update",
    "commerceorggovernance.services.list",
    "commerceprice.privateoffers.cancel",
    "commerceprice.privateoffers.create",
    "commerceprice.privateoffers.delete",
    "commerceprice.privateoffers.get",
    "commerceprice.privateoffers.list",
    "commerceprice.privateoffers.publish",
    "commerceprice.privateoffers.update",
    "composer.dags.execute",
    "composer.dags.get",
    "composer.dags.getSourceCode",
    "composer.dags.list",
    "composer.environments.create",
    "composer.environments.delete",
    "composer.environments.get",
    "composer.environments.list",
    "composer.environments.update",
    "composer.imageversions.list",
    "composer.operations.delete",
    "composer.operations.get",
    "composer.operations.list",
    "compute.acceleratorTypes.get",
    "compute.acceleratorTypes.list",
    "compute.addresses.create",
    "compute.addresses.createInternal",
    "compute.addresses.delete",
    "compute.addresses.deleteInternal",
    "compute.addresses.get",
    "compute.addresses.list",
    "compute.addresses.setLabels",
    "compute.addresses.use",
    "compute.addresses.useInternal",
    "compute.autoscalers.create",
    "compute.autoscalers.delete",
    "compute.autoscalers.get",
    "compute.autoscalers.list",
    "compute.autoscalers.update",
    "compute.backendBuckets.addSignedUrlKey",
    "compute.backendBuckets.create",
    "compute.backendBuckets.delete",
    "compute.backendBuckets.deleteSignedUrlKey",
    "compute.backendBuckets.get",
    "compute.backendBuckets.getIamPolicy",
    "compute.backendBuckets.list",
    "compute.backendBuckets.setIamPolicy",
    "compute.backendBuckets.setSecurityPolicy",
    "compute.backendBuckets.update",
    "compute.backendBuckets.use",
    "compute.backendServices.addSignedUrlKey",
    "compute.backendServices.create",
    "compute.backendServices.delete",
    "compute.backendServices.deleteSignedUrlKey",
    "compute.backendServices.get",
    "compute.backendServices.getIamPolicy",
    "compute.backendServices.list",
    "compute.backendServices.setIamPolicy",
    "compute.backendServices.setSecurityPolicy",
    "compute.backendServices.update",
    "compute.backendServices.use",
    "compute.commitments.create",
    "compute.commitments.get",
    "compute.commitments.list",
    "compute.commitments.update",
    "compute.commitments.updateReservations",
    "compute.diskTypes.get",
    "compute.diskTypes.list",
    "compute.disks.addResourcePolicies",
    "compute.disks.create",
    "compute.disks.createSnapshot",
    "compute.disks.createTagBinding",
    "compute.disks.delete",
    "compute.disks.deleteTagBinding",
    "compute.disks.get",
    "compute.disks.getIamPolicy",
    "compute.disks.list",
    "compute.disks.listEffectiveTags",
    "compute.disks.listTagBindings",
    "compute.disks.removeResourcePolicies",
    "compute.disks.resize",
    "compute.disks.setIamPolicy",
    "compute.disks.setLabels",
    "compute.disks.update",
    "compute.disks.use",
    "compute.disks.useReadOnly",
    "compute.externalVpnGateways.create",
    "compute.externalVpnGateways.delete",
    "compute.externalVpnGateways.get",
    "compute.externalVpnGateways.list",
    "compute.externalVpnGateways.setLabels",
    "compute.externalVpnGateways.use",
    "compute.firewallPolicies.cloneRules",
    "compute.firewallPolicies.create",
    "compute.firewallPolicies.delete",
    "compute.firewallPolicies.get",
    "compute.firewallPolicies.getIamPolicy",
    "compute.firewallPolicies.list",
    "compute.firewallPolicies.setIamPolicy",
    "compute.firewallPolicies.update",
    "compute.firewallPolicies.use",
    "compute.firewalls.create",
    "compute.firewalls.delete",
    "compute.firewalls.get",
    "compute.firewalls.list",
    "compute.firewalls.update",
    "compute.forwardingRules.create",
    "compute.forwardingRules.delete",
    "compute.forwardingRules.get",
    "compute.forwardingRules.list",
    "compute.forwardingRules.pscCreate",
    "compute.forwardingRules.pscDelete",
    "compute.forwardingRules.pscSetLabels",
    "compute.forwardingRules.pscSetTarget",
    "compute.forwardingRules.pscUpdate",
    "compute.forwardingRules.setLabels",
    "compute.forwardingRules.setTarget",
    "compute.forwardingRules.update",
    "compute.forwardingRules.use",
    "compute.globalAddresses.create",
    "compute.globalAddresses.createInternal",
    "compute.globalAddresses.delete",
    "compute.globalAddresses.deleteInternal",
    "compute.globalAddresses.get",
    "compute.globalAddresses.list",
    "compute.globalAddresses.setLabels",
    "compute.globalAddresses.use",
    "compute.globalForwardingRules.create",
    "compute.globalForwardingRules.delete",
    "compute.globalForwardingRules.get",
    "compute.globalForwardingRules.list",
    "compute.globalForwardingRules.pscCreate",
    "compute.globalForwardingRules.pscDelete",
    "compute.globalForwardingRules.pscGet",
    "compute.globalForwardingRules.pscSetLabels",
    "compute.globalForwardingRules.pscSetTarget",
    "compute.globalForwardingRules.pscUpdate",
    "compute.globalForwardingRules.setLabels",
    "compute.globalForwardingRules.setTarget",
    "compute.globalForwardingRules.update",
    "compute.globalNetworkEndpointGroups.attachNetworkEndpoints",
    "compute.globalNetworkEndpointGroups.create",
    "compute.globalNetworkEndpointGroups.delete",
    "compute.globalNetworkEndpointGroups.detachNetworkEndpoints",
    "compute.globalNetworkEndpointGroups.get",
    "compute.globalNetworkEndpointGroups.list",
    "compute.globalNetworkEndpointGroups.use",
    "compute.globalOperations.delete",
    "compute.globalOperations.get",
    "compute.globalOperations.getIamPolicy",
    "compute.globalOperations.list",
    "compute.globalOperations.setIamPolicy",
    "compute.globalPublicDelegatedPrefixes.create",
    "compute.globalPublicDelegatedPrefixes.delete",
    "compute.globalPublicDelegatedPrefixes.get",
    "compute.globalPublicDelegatedPrefixes.list",
    "compute.globalPublicDelegatedPrefixes.update",
    "compute.globalPublicDelegatedPrefixes.updatePolicy",
    "compute.globalPublicDelegatedPrefixes.use",
    "compute.healthChecks.create",
    "compute.healthChecks.delete",
    "compute.healthChecks.get",
    "compute.healthChecks.list",
    "compute.healthChecks.update",
    "compute.healthChecks.use",
    "compute.healthChecks.useReadOnly",
    "compute.httpHealthChecks.create",
    "compute.httpHealthChecks.delete",
    "compute.httpHealthChecks.get",
    "compute.httpHealthChecks.list",
    "compute.httpHealthChecks.update",
    "compute.httpHealthChecks.use",
    "compute.httpHealthChecks.useReadOnly",
    "compute.httpsHealthChecks.create",
    "compute.httpsHealthChecks.delete",
    "compute.httpsHealthChecks.get",
    "compute.httpsHealthChecks.list",
    "compute.httpsHealthChecks.update",
    "compute.httpsHealthChecks.use",
    "compute.httpsHealthChecks.useReadOnly",
    "compute.images.create",
    "compute.images.createTagBinding",
    "compute.images.delete",
    "compute.images.deleteTagBinding",
    "compute.images.deprecate",
    "compute.images.get",
    "compute.images.getFromFamily",
    "compute.images.getIamPolicy",
    "compute.images.list",
    "compute.images.listEffectiveTags",
    "compute.images.listTagBindings",
    "compute.images.setIamPolicy",
    "compute.images.setLabels",
    "compute.images.update",
    "compute.images.useReadOnly",
    "compute.instanceGroupManagers.create",
    "compute.instanceGroupManagers.delete",
    "compute.instanceGroupManagers.get",
    "compute.instanceGroupManagers.list",
    "compute.instanceGroupManagers.update",
    "compute.instanceGroupManagers.use",
    "compute.instanceGroups.create",
    "compute.instanceGroups.delete",
    "compute.instanceGroups.get",
    "compute.instanceGroups.list",
    "compute.instanceGroups.update",
    "compute.instanceGroups.use",
    "compute.instanceTemplates.create",
    "compute.instanceTemplates.delete",
    "compute.instanceTemplates.get",
    "compute.instanceTemplates.getIamPolicy",
    "compute.instanceTemplates.list",
    "compute.instanceTemplates.setIamPolicy",
    "compute.instanceTemplates.useReadOnly",
    "compute.instances.addAccessConfig",
    "compute.instances.addMaintenancePolicies",
    "compute.instances.addResourcePolicies",
    "compute.instances.attachDisk",
    "compute.instances.create",
    "compute.instances.createTagBinding",
    "compute.instances.delete",
    "compute.instances.deleteAccessConfig",
    "compute.instances.deleteTagBinding",
    "compute.instances.detachDisk",
    "compute.instances.get",
    "compute.instances.getEffectiveFirewalls",
    "compute.instances.getGuestAttributes",
    "compute.instances.getIamPolicy",
    "compute.instances.getScreenshot",
    "compute.instances.getSerialPortOutput",
    "compute.instances.getShieldedInstanceIdentity",
    "compute.instances.getShieldedVmIdentity",
    "compute.instances.list",
    "compute.instances.listEffectiveTags",
    "compute.instances.listReferrers",
    "compute.instances.listTagBindings",
    "compute.instances.osAdminLogin",
    "compute.instances.osLogin",
    "compute.instances.removeMaintenancePolicies",
    "compute.instances.removeResourcePolicies",
    "compute.instances.reset",
    "compute.instances.resume",
    "compute.instances.sendDiagnosticInterrupt",
    "compute.instances.setDeletionProtection",
    "compute.instances.setDiskAutoDelete",
    "compute.instances.setIamPolicy",
    "compute.instances.setLabels",
    "compute.instances.setMachineResources",
    "compute.instances.setMachineType",
    "compute.instances.setMetadata",
    "compute.instances.setMinCpuPlatform",
    "compute.instances.setName",
    "compute.instances.setScheduling",
    "compute.instances.setServiceAccount",
    "compute.instances.setShieldedInstanceIntegrityPolicy",
    "compute.instances.setShieldedVmIntegrityPolicy",
    "compute.instances.setTags",
    "compute.instances.start",
    "compute.instances.startWithEncryptionKey",
    "compute.instances.stop",
    "compute.instances.suspend",
    "compute.instances.update",
    "compute.instances.updateAccessConfig",
    "compute.instances.updateDisplayDevice",
    "compute.instances.updateNetworkInterface",
    "compute.instances.updateSecurity",
    "compute.instances.updateShieldedInstanceConfig",
    "compute.instances.updateShieldedVmConfig",
    "compute.instances.use",
    "compute.instances.useReadOnly",
    "compute.interconnectAttachments.create",
    "compute.interconnectAttachments.delete",
    "compute.interconnectAttachments.get",
    "compute.interconnectAttachments.list",
    "compute.interconnectAttachments.setLabels",
    "compute.interconnectAttachments.update",
    "compute.interconnectAttachments.use",
    "compute.interconnectLocations.get",
    "compute.interconnectLocations.list",
    "compute.interconnects.create",
    "compute.interconnects.delete",
    "compute.interconnects.get",
    "compute.interconnects.list",
    "compute.interconnects.setLabels",
    "compute.interconnects.update",
    "compute.interconnects.use",
    "compute.licenseCodes.get",
    "compute.licenseCodes.getIamPolicy",
    "compute.licenseCodes.list",
    "compute.licenseCodes.setIamPolicy",
    "compute.licenseCodes.update",
    "compute.licenseCodes.use",
    "compute.licenses.create",
    "compute.licenses.delete",
    "compute.licenses.get",
    "compute.licenses.getIamPolicy",
    "compute.licenses.list",
    "compute.licenses.setIamPolicy",
    "compute.machineImages.create",
    "compute.machineImages.delete",
    "compute.machineImages.get",
    "compute.machineImages.getIamPolicy",
    "compute.machineImages.list",
    "compute.machineImages.setIamPolicy",
    "compute.machineImages.useReadOnly",
    "compute.machineTypes.get",
    "compute.machineTypes.list",
    "compute.maintenancePolicies.create",
    "compute.maintenancePolicies.delete",
    "compute.maintenancePolicies.get",
    "compute.maintenancePolicies.getIamPolicy",
    "compute.maintenancePolicies.list",
    "compute.maintenancePolicies.setIamPolicy",
    "compute.maintenancePolicies.use",
    "compute.networkAttachments.create",
    "compute.networkAttachments.delete",
    "compute.networkAttachments.get",
    "compute.networkAttachments.list",
    "compute.networkEdgeSecurityServices.create",
    "compute.networkEdgeSecurityServices.delete",
    "compute.networkEdgeSecurityServices.get",
    "compute.networkEdgeSecurityServices.list",
    "compute.networkEdgeSecurityServices.update",
    "compute.networkEndpointGroups.attachNetworkEndpoints",
    "compute.networkEndpointGroups.create",
    "compute.networkEndpointGroups.delete",
    "compute.networkEndpointGroups.detachNetworkEndpoints",
    "compute.networkEndpointGroups.get",
    "compute.networkEndpointGroups.getIamPolicy",
    "compute.networkEndpointGroups.list",
    "compute.networkEndpointGroups.setIamPolicy",
    "compute.networkEndpointGroups.use",
    "compute.networks.access",
    "compute.networks.addPeering",
    "compute.networks.create",
    "compute.networks.delete",
    "compute.networks.get",
    "compute.networks.getEffectiveFirewalls",
    "compute.networks.getRegionEffectiveFirewalls",
    "compute.networks.list",
    "compute.networks.listPeeringRoutes",
    "compute.networks.mirror",
    "compute.networks.removePeering",
    "compute.networks.setFirewallPolicy",
    "compute.networks.switchToCustomMode",
    "compute.networks.update",
    "compute.networks.updatePeering",
    "compute.networks.updatePolicy",
    "compute.networks.use",
    "compute.networks.useExternalIp",
    "compute.nodeGroups.addNodes",
    "compute.nodeGroups.create",
    "compute.nodeGroups.delete",
    "compute.nodeGroups.deleteNodes",
    "compute.nodeGroups.get",
    "compute.nodeGroups.getIamPolicy",
    "compute.nodeGroups.list",
    "compute.nodeGroups.setIamPolicy",
    "compute.nodeGroups.setNodeTemplate",
    "compute.nodeGroups.update",
    "compute.nodeTemplates.create",
    "compute.nodeTemplates.delete",
    "compute.nodeTemplates.get",
    "compute.nodeTemplates.getIamPolicy",
    "compute.nodeTemplates.list",
    "compute.nodeTemplates.setIamPolicy",
    "compute.nodeTypes.get",
    "compute.nodeTypes.list",
    "compute.organizations.administerXpn",
    "compute.packetMirrorings.create",
    "compute.packetMirrorings.delete",
    "compute.packetMirrorings.get",
    "compute.packetMirrorings.list",
    "compute.packetMirrorings.update",
    "compute.projects.get",
    "compute.projects.setCommonInstanceMetadata",
    "compute.projects.setDefaultNetworkTier",
    "compute.projects.setDefaultServiceAccount",
    "compute.projects.setUsageExportBucket",
    "compute.publicAdvertisedPrefixes.create",
    "compute.publicAdvertisedPrefixes.delete",
    "compute.publicAdvertisedPrefixes.get",
    "compute.publicAdvertisedPrefixes.list",
    "compute.publicAdvertisedPrefixes.update",
    "compute.publicAdvertisedPrefixes.updatePolicy",
    "compute.publicAdvertisedPrefixes.use",
    "compute.publicDelegatedPrefixes.create",
    "compute.publicDelegatedPrefixes.delete",
    "compute.publicDelegatedPrefixes.get",
    "compute.publicDelegatedPrefixes.list",
    "compute.publicDelegatedPrefixes.update",
    "compute.publicDelegatedPrefixes.updatePolicy",
    "compute.publicDelegatedPrefixes.use",
    "compute.regionBackendServices.create",
    "compute.regionBackendServices.delete",
    "compute.regionBackendServices.get",
    "compute.regionBackendServices.getIamPolicy",
    "compute.regionBackendServices.list",
    "compute.regionBackendServices.setIamPolicy",
    "compute.regionBackendServices.setSecurityPolicy",
    "compute.regionBackendServices.update",
    "compute.regionBackendServices.use",
    "compute.regionFirewallPolicies.cloneRules",
    "compute.regionFirewallPolicies.create",
    "compute.regionFirewallPolicies.delete",
    "compute.regionFirewallPolicies.get",
    "compute.regionFirewallPolicies.getIamPolicy",
    "compute.regionFirewallPolicies.list",
    "compute.regionFirewallPolicies.setIamPolicy",
    "compute.regionFirewallPolicies.update",
    "compute.regionFirewallPolicies.use",
    "compute.regionHealthCheckServices.create",
    "compute.regionHealthCheckServices.delete",
    "compute.regionHealthCheckServices.get",
    "compute.regionHealthCheckServices.list",
    "compute.regionHealthCheckServices.update",
    "compute.regionHealthCheckServices.use",
    "compute.regionHealthChecks.create",
    "compute.regionHealthChecks.delete",
    "compute.regionHealthChecks.get",
    "compute.regionHealthChecks.list",
    "compute.regionHealthChecks.update",
    "compute.regionHealthChecks.use",
    "compute.regionHealthChecks.useReadOnly",
    "compute.regionNetworkEndpointGroups.create",
    "compute.regionNetworkEndpointGroups.delete",
    "compute.regionNetworkEndpointGroups.get",
    "compute.regionNetworkEndpointGroups.list",
    "compute.regionNetworkEndpointGroups.use",
    "compute.regionNotificationEndpoints.create",
    "compute.regionNotificationEndpoints.delete",
    "compute.regionNotificationEndpoints.get",
    "compute.regionNotificationEndpoints.list",
    "compute.regionNotificationEndpoints.update",
    "compute.regionNotificationEndpoints.use",
    "compute.regionOperations.delete",
    "compute.regionOperations.get",
    "compute.regionOperations.getIamPolicy",
    "compute.regionOperations.list",
    "compute.regionOperations.setIamPolicy",
    "compute.regionSecurityPolicies.create",
    "compute.regionSecurityPolicies.delete",
    "compute.regionSecurityPolicies.get",
    "compute.regionSecurityPolicies.list",
    "compute.regionSecurityPolicies.update",
    "compute.regionSecurityPolicies.use",
    "compute.regionSslCertificates.create",
    "compute.regionSslCertificates.delete",
    "compute.regionSslCertificates.get",
    "compute.regionSslCertificates.list",
    "compute.regionSslPolicies.create",
    "compute.regionSslPolicies.delete",
    "compute.regionSslPolicies.get",
    "compute.regionSslPolicies.list",
    "compute.regionSslPolicies.listAvailableFeatures",
    "compute.regionSslPolicies.update",
    "compute.regionSslPolicies.use",
    "compute.regionTargetHttpProxies.create",
    "compute.regionTargetHttpProxies.delete",
    "compute.regionTargetHttpProxies.get",
    "compute.regionTargetHttpProxies.list",
    "compute.regionTargetHttpProxies.setUrlMap",
    "compute.regionTargetHttpProxies.update",
    "compute.regionTargetHttpProxies.use",
    "compute.regionTargetHttpsProxies.create",
    "compute.regionTargetHttpsProxies.delete",
    "compute.regionTargetHttpsProxies.get",
    "compute.regionTargetHttpsProxies.list",
    "compute.regionTargetHttpsProxies.setSslCertificates",
    "compute.regionTargetHttpsProxies.setUrlMap",
    "compute.regionTargetHttpsProxies.update",
    "compute.regionTargetHttpsProxies.use",
    "compute.regionTargetTcpProxies.create",
    "compute.regionTargetTcpProxies.delete",
    "compute.regionTargetTcpProxies.get",
    "compute.regionTargetTcpProxies.list",
    "compute.regionTargetTcpProxies.use",
    "compute.regionUrlMaps.create",
    "compute.regionUrlMaps.delete",
    "compute.regionUrlMaps.get",
    "compute.regionUrlMaps.invalidateCache",
    "compute.regionUrlMaps.list",
    "compute.regionUrlMaps.update",
    "compute.regionUrlMaps.use",
    "compute.regionUrlMaps.validate",
    "compute.regions.get",
    "compute.regions.list",
    "compute.reservations.create",
    "compute.reservations.delete",
    "compute.reservations.get",
    "compute.reservations.list",
    "compute.reservations.resize",
    "compute.reservations.update",
    "compute.resourcePolicies.create",
    "compute.resourcePolicies.delete",
    "compute.resourcePolicies.get",
    "compute.resourcePolicies.getIamPolicy",
    "compute.resourcePolicies.list",
    "compute.resourcePolicies.setIamPolicy",
    "compute.resourcePolicies.use",
    "compute.routers.create",
    "compute.routers.delete",
    "compute.routers.get",
    "compute.routers.list",
    "compute.routers.update",
    "compute.routers.use",
    "compute.routes.create",
    "compute.routes.delete",
    "compute.routes.get",
    "compute.routes.list",
    "compute.securityPolicies.create",
    "compute.securityPolicies.delete",
    "compute.securityPolicies.get",
    "compute.securityPolicies.getIamPolicy",
    "compute.securityPolicies.list",
    "compute.securityPolicies.setIamPolicy",
    "compute.securityPolicies.setLabels",
    "compute.securityPolicies.update",
    "compute.securityPolicies.use",
    "compute.serviceAttachments.create",
    "compute.serviceAttachments.delete",
    "compute.serviceAttachments.get",
    "compute.serviceAttachments.getIamPolicy",
    "compute.serviceAttachments.list",
    "compute.serviceAttachments.setIamPolicy",
    "compute.serviceAttachments.update",
    "compute.serviceAttachments.use",
    "compute.snapshots.create",
    "compute.snapshots.createTagBinding",
    "compute.snapshots.delete",
    "compute.snapshots.deleteTagBinding",
    "compute.snapshots.get",
    "compute.snapshots.getIamPolicy",
    "compute.snapshots.list",
    "compute.snapshots.listEffectiveTags",
    "compute.snapshots.listTagBindings",
    "compute.snapshots.setIamPolicy",
    "compute.snapshots.setLabels",
    "compute.snapshots.useReadOnly",
    "compute.sslCertificates.create",
    "compute.sslCertificates.delete",
    "compute.sslCertificates.get",
    "compute.sslCertificates.list",
    "compute.sslPolicies.create",
    "compute.sslPolicies.delete",
    "compute.sslPolicies.get",
    "compute.sslPolicies.list",
    "compute.sslPolicies.listAvailableFeatures",
    "compute.sslPolicies.update",
    "compute.sslPolicies.use",
    "compute.subnetworks.create",
    "compute.subnetworks.delete",
    "compute.subnetworks.expandIpCidrRange",
    "compute.subnetworks.get",
    "compute.subnetworks.getIamPolicy",
    "compute.subnetworks.list",
    "compute.subnetworks.mirror",
    "compute.subnetworks.setIamPolicy",
    "compute.subnetworks.setPrivateIpGoogleAccess",
    "compute.subnetworks.update",
    "compute.subnetworks.use",
    "compute.subnetworks.useExternalIp",
    "compute.targetGrpcProxies.create",
    "compute.targetGrpcProxies.delete",
    "compute.targetGrpcProxies.get",
    "compute.targetGrpcProxies.list",
    "compute.targetGrpcProxies.update",
    "compute.targetGrpcProxies.use",
    "compute.targetHttpProxies.create",
    "compute.targetHttpProxies.delete",
    "compute.targetHttpProxies.get",
    "compute.targetHttpProxies.list",
    "compute.targetHttpProxies.setUrlMap",
    "compute.targetHttpProxies.update",
    "compute.targetHttpProxies.use",
    "compute.targetHttpsProxies.create",
    "compute.targetHttpsProxies.delete",
    "compute.targetHttpsProxies.get",
    "compute.targetHttpsProxies.list",
    "compute.targetHttpsProxies.setCertificateMap",
    "compute.targetHttpsProxies.setQuicOverride",
    "compute.targetHttpsProxies.setSslCertificates",
    "compute.targetHttpsProxies.setSslPolicy",
    "compute.targetHttpsProxies.setUrlMap",
    "compute.targetHttpsProxies.update",
    "compute.targetHttpsProxies.use",
    "compute.targetInstances.create",
    "compute.targetInstances.delete",
    "compute.targetInstances.get",
    "compute.targetInstances.list",
    "compute.targetInstances.use",
    "compute.targetPools.addHealthCheck",
    "compute.targetPools.addInstance",
    "compute.targetPools.create",
    "compute.targetPools.delete",
    "compute.targetPools.get",
    "compute.targetPools.list",
    "compute.targetPools.removeHealthCheck",
    "compute.targetPools.removeInstance",
    "compute.targetPools.update",
    "compute.targetPools.use",
    "compute.targetSslProxies.create",
    "compute.targetSslProxies.delete",
    "compute.targetSslProxies.get",
    "compute.targetSslProxies.list",
    "compute.targetSslProxies.setBackendService",
    "compute.targetSslProxies.setCertificateMap",
    "compute.targetSslProxies.setProxyHeader",
    "compute.targetSslProxies.setSslCertificates",
    "compute.targetSslProxies.setSslPolicy",
    "compute.targetSslProxies.update",
    "compute.targetSslProxies.use",
    "compute.targetTcpProxies.create",
    "compute.targetTcpProxies.delete",
    "compute.targetTcpProxies.get",
    "compute.targetTcpProxies.list",
    "compute.targetTcpProxies.update",
    "compute.targetTcpProxies.use",
    "compute.targetVpnGateways.create",
    "compute.targetVpnGateways.delete",
    "compute.targetVpnGateways.get",
    "compute.targetVpnGateways.list",
    "compute.targetVpnGateways.setLabels",
    "compute.targetVpnGateways.use",
    "compute.urlMaps.create",
    "compute.urlMaps.delete",
    "compute.urlMaps.get",
    "compute.urlMaps.invalidateCache",
    "compute.urlMaps.list",
    "compute.urlMaps.update",
    "compute.urlMaps.use",
    "compute.urlMaps.validate",
    "compute.vpnGateways.create",
    "compute.vpnGateways.delete",
    "compute.vpnGateways.get",
    "compute.vpnGateways.list",
    "compute.vpnGateways.setLabels",
    "compute.vpnGateways.use",
    "compute.vpnTunnels.create",
    "compute.vpnTunnels.delete",
    "compute.vpnTunnels.get",
    "compute.vpnTunnels.list",
    "compute.vpnTunnels.setLabels",
    "compute.zoneOperations.delete",
    "compute.zoneOperations.get",
    "compute.zoneOperations.getIamPolicy",
    "compute.zoneOperations.list",
    "compute.zoneOperations.setIamPolicy",
    "compute.zones.get",
    "compute.zones.list",
    "connectors.actions.execute",
    "connectors.actions.list",
    "connectors.connections.create",
    "connectors.connections.delete",
    "connectors.connections.executeSqlQuery",
    "connectors.connections.get",
    "connectors.connections.getConnectionSchemaMetadata",
    "connectors.connections.getIamPolicy",
    "connectors.connections.getRuntimeActionSchema",
    "connectors.connections.getRuntimeEntitySchema",
    "connectors.connections.list",
    "connectors.connections.setIamPolicy",
    "connectors.connections.update",
    "connectors.connectors.get",
    "connectors.connectors.list",
    "connectors.entities.create",
    "connectors.entities.delete",
    "connectors.entities.deleteEntitiesWithConditions",
    "connectors.entities.get",
    "connectors.entities.list",
    "connectors.entities.update",
    "connectors.entities.updateEntitiesWithConditions",
    "connectors.entityTypes.list",
    "connectors.locations.get",
    "connectors.locations.list",
    "connectors.operations.cancel",
    "connectors.operations.delete",
    "connectors.operations.get",
    "connectors.operations.list",
    "connectors.providers.get",
    "connectors.providers.list",
    "connectors.runtimeconfig.get",
    "connectors.schemaMetadata.refresh",
    "connectors.versions.get",
    "connectors.versions.list",
    "consumerprocurement.consents.allowProjectGrant",
    "consumerprocurement.consents.check",
    "consumerprocurement.consents.grant",
    "consumerprocurement.consents.list",
    "consumerprocurement.consents.revoke",
    "consumerprocurement.entitlements.get",
    "consumerprocurement.entitlements.list",
    "consumerprocurement.freeTrials.create",
    "consumerprocurement.freeTrials.get",
    "consumerprocurement.freeTrials.list",
    "contactcenteraiplatform.contactCenters.create",
    "contactcenteraiplatform.contactCenters.delete",
    "contactcenteraiplatform.contactCenters.get",
    "contactcenteraiplatform.contactCenters.list",
    "contactcenteraiplatform.contactCenters.update",
    "contactcenteraiplatform.locations.get",
    "contactcenteraiplatform.locations.list",
    "contactcenteraiplatform.operations.cancel",
    "contactcenteraiplatform.operations.delete",
    "contactcenteraiplatform.operations.get",
    "contactcenteraiplatform.operations.list",
    "contactcenterinsights.analyses.create",
    "contactcenterinsights.analyses.delete",
    "contactcenterinsights.analyses.get",
    "contactcenterinsights.analyses.list",
    "contactcenterinsights.conversations.create",
    "contactcenterinsights.conversations.delete",
    "contactcenterinsights.conversations.export",
    "contactcenterinsights.conversations.get",
    "contactcenterinsights.conversations.list",
    "contactcenterinsights.conversations.update",
    "contactcenterinsights.issueModels.create",
    "contactcenterinsights.issueModels.delete",
    "contactcenterinsights.issueModels.deploy",
    "contactcenterinsights.issueModels.get",
    "contactcenterinsights.issueModels.list",
    "contactcenterinsights.issueModels.undeploy",
    "contactcenterinsights.issueModels.update",
    "contactcenterinsights.issues.get",
    "contactcenterinsights.issues.list",
    "contactcenterinsights.issues.update",
    "contactcenterinsights.operations.get",
    "contactcenterinsights.operations.list",
    "contactcenterinsights.phraseMatchers.create",
    "contactcenterinsights.phraseMatchers.delete",
    "contactcenterinsights.phraseMatchers.get",
    "contactcenterinsights.phraseMatchers.list",
    "contactcenterinsights.phraseMatchers.update",
    "contactcenterinsights.settings.get",
    "contactcenterinsights.settings.update",
    "contactcenterinsights.views.create",
    "contactcenterinsights.views.delete",
    "contactcenterinsights.views.get",
    "contactcenterinsights.views.list",
    "contactcenterinsights.views.update",
    "container.apiServices.create",
    "container.apiServices.delete",
    "container.apiServices.get",
    "container.apiServices.getStatus",
    "container.apiServices.list",
    "container.apiServices.update",
    "container.apiServices.updateStatus",
    "container.auditSinks.create",
    "container.auditSinks.delete",
    "container.auditSinks.get",
    "container.auditSinks.list",
    "container.auditSinks.update",
    "container.backendConfigs.create",
    "container.backendConfigs.delete",
    "container.backendConfigs.get",
    "container.backendConfigs.list",
    "container.backendConfigs.update",
    "container.bindings.create",
    "container.bindings.delete",
    "container.bindings.get",
    "container.bindings.list",
    "container.bindings.update",
    "container.certificateSigningRequests.approve",
    "container.certificateSigningRequests.create",
    "container.certificateSigningRequests.delete",
    "container.certificateSigningRequests.get",
    "container.certificateSigningRequests.getStatus",
    "container.certificateSigningRequests.list",
    "container.certificateSigningRequests.update",
    "container.certificateSigningRequests.updateStatus",
    "container.clusterRoleBindings.create",
    "container.clusterRoleBindings.delete",
    "container.clusterRoleBindings.get",
    "container.clusterRoleBindings.list",
    "container.clusterRoleBindings.update",
    "container.clusterRoles.bind",
    "container.clusterRoles.create",
    "container.clusterRoles.delete",
    "container.clusterRoles.escalate",
    "container.clusterRoles.get",
    "container.clusterRoles.list",
    "container.clusterRoles.update",
    "container.clusters.create",
    "container.clusters.createTagBinding",
    "container.clusters.delete",
    "container.clusters.deleteTagBinding",
    "container.clusters.get",
    "container.clusters.getCredentials",
    "container.clusters.list",
    "container.clusters.listEffectiveTags",
    "container.clusters.listTagBindings",
    "container.clusters.update",
    "container.componentStatuses.get",
    "container.componentStatuses.list",
    "container.configMaps.create",
    "container.configMaps.delete",
    "container.configMaps.get",
    "container.configMaps.list",
    "container.configMaps.update",
    "container.controllerRevisions.create",
    "container.controllerRevisions.delete",
    "container.controllerRevisions.get",
    "container.controllerRevisions.list",
    "container.controllerRevisions.update",
    "container.cronJobs.create",
    "container.cronJobs.delete",
    "container.cronJobs.get",
    "container.cronJobs.getStatus",
    "container.cronJobs.list",
    "container.cronJobs.update",
    "container.cronJobs.updateStatus",
    "container.csiDrivers.create",
    "container.csiDrivers.delete",
    "container.csiDrivers.get",
    "container.csiDrivers.list",
    "container.csiDrivers.update",
    "container.csiNodeInfos.create",
    "container.csiNodeInfos.delete",
    "container.csiNodeInfos.get",
    "container.csiNodeInfos.list",
    "container.csiNodeInfos.update",
    "container.csiNodes.create",
    "container.csiNodes.delete",
    "container.csiNodes.get",
    "container.csiNodes.list",
    "container.csiNodes.update",
    "container.customResourceDefinitions.create",
    "container.customResourceDefinitions.delete",
    "container.customResourceDefinitions.get",
    "container.customResourceDefinitions.getStatus",
    "container.customResourceDefinitions.list",
    "container.customResourceDefinitions.update",
    "container.customResourceDefinitions.updateStatus",
    "container.daemonSets.create",
    "container.daemonSets.delete",
    "container.daemonSets.get",
    "container.daemonSets.getStatus",
    "container.daemonSets.list",
    "container.daemonSets.update",
    "container.daemonSets.updateStatus",
    "container.deployments.create",
    "container.deployments.delete",
    "container.deployments.get",
    "container.deployments.getScale",
    "container.deployments.getStatus",
    "container.deployments.list",
    "container.deployments.rollback",
    "container.deployments.update",
    "container.deployments.updateScale",
    "container.deployments.updateStatus",
    "container.endpointSlices.create",
    "container.endpointSlices.delete",
    "container.endpointSlices.get",
    "container.endpointSlices.list",
    "container.endpointSlices.update",
    "container.endpoints.create",
    "container.endpoints.delete",
    "container.endpoints.get",
    "container.endpoints.list",
    "container.endpoints.update",
    "container.events.create",
    "container.events.delete",
    "container.events.get",
    "container.events.list",
    "container.events.update",
    "container.frontendConfigs.create",
    "container.frontendConfigs.delete",
    "container.frontendConfigs.get",
    "container.frontendConfigs.list",
    "container.frontendConfigs.update",
    "container.horizontalPodAutoscalers.create",
    "container.horizontalPodAutoscalers.delete",
    "container.horizontalPodAutoscalers.get",
    "container.horizontalPodAutoscalers.getStatus",
    "container.horizontalPodAutoscalers.list",
    "container.horizontalPodAutoscalers.update",
    "container.horizontalPodAutoscalers.updateStatus",
    "container.hostServiceAgent.use",
    "container.ingresses.create",
    "container.ingresses.delete",
    "container.ingresses.get",
    "container.ingresses.getStatus",
    "container.ingresses.list",
    "container.ingresses.update",
    "container.ingresses.updateStatus",
    "container.initializerConfigurations.create",
    "container.initializerConfigurations.delete",
    "container.initializerConfigurations.get",
    "container.initializerConfigurations.list",
    "container.initializerConfigurations.update",
    "container.jobs.create",
    "container.jobs.delete",
    "container.jobs.get",
    "container.jobs.getStatus",
    "container.jobs.list",
    "container.jobs.update",
    "container.jobs.updateStatus",
    "container.leases.create",
    "container.leases.delete",
    "container.leases.get",
    "container.leases.list",
    "container.leases.update",
    "container.limitRanges.create",
    "container.limitRanges.delete",
    "container.limitRanges.get",
    "container.limitRanges.list",
    "container.limitRanges.update",
    "container.localSubjectAccessReviews.create",
    "container.localSubjectAccessReviews.list",
    "container.managedCertificates.create",
    "container.managedCertificates.delete",
    "container.managedCertificates.get",
    "container.managedCertificates.list",
    "container.managedCertificates.update",
    "container.mutatingWebhookConfigurations.create",
    "container.mutatingWebhookConfigurations.delete",
    "container.mutatingWebhookConfigurations.get",
    "container.mutatingWebhookConfigurations.list",
    "container.mutatingWebhookConfigurations.update",
    "container.namespaces.create",
    "container.namespaces.delete",
    "container.namespaces.finalize",
    "container.namespaces.get",
    "container.namespaces.getStatus",
    "container.namespaces.list",
    "container.namespaces.update",
    "container.namespaces.updateStatus",
    "container.networkPolicies.create",
    "container.networkPolicies.delete",
    "container.networkPolicies.get",
    "container.networkPolicies.list",
    "container.networkPolicies.update",
    "container.nodes.create",
    "container.nodes.delete",
    "container.nodes.get",
    "container.nodes.getStatus",
    "container.nodes.list",
    "container.nodes.proxy",
    "container.nodes.update",
    "container.nodes.updateStatus",
    "container.operations.get",
    "container.operations.list",
    "container.persistentVolumeClaims.create",
    "container.persistentVolumeClaims.delete",
    "container.persistentVolumeClaims.get",
    "container.persistentVolumeClaims.getStatus",
    "container.persistentVolumeClaims.list",
    "container.persistentVolumeClaims.update",
    "container.persistentVolumeClaims.updateStatus",
    "container.persistentVolumes.create",
    "container.persistentVolumes.delete",
    "container.persistentVolumes.get",
    "container.persistentVolumes.getStatus",
    "container.persistentVolumes.list",
    "container.persistentVolumes.update",
    "container.persistentVolumes.updateStatus",
    "container.petSets.create",
    "container.petSets.delete",
    "container.petSets.get",
    "container.petSets.list",
    "container.petSets.update",
    "container.petSets.updateStatus",
    "container.podDisruptionBudgets.create",
    "container.podDisruptionBudgets.delete",
    "container.podDisruptionBudgets.get",
    "container.podDisruptionBudgets.getStatus",
    "container.podDisruptionBudgets.list",
    "container.podDisruptionBudgets.update",
    "container.podDisruptionBudgets.updateStatus",
    "container.podPresets.create",
    "container.podPresets.delete",
    "container.podPresets.get",
    "container.podPresets.list",
    "container.podPresets.update",
    "container.podSecurityPolicies.create",
    "container.podSecurityPolicies.delete",
    "container.podSecurityPolicies.get",
    "container.podSecurityPolicies.list",
    "container.podSecurityPolicies.update",
    "container.podSecurityPolicies.use",
    "container.podTemplates.create",
    "container.podTemplates.delete",
    "container.podTemplates.get",
    "container.podTemplates.list",
    "container.podTemplates.update",
    "container.pods.attach",
    "container.pods.create",
    "container.pods.delete",
    "container.pods.evict",
    "container.pods.exec",
    "container.pods.get",
    "container.pods.getLogs",
    "container.pods.getStatus",
    "container.pods.initialize",
    "container.pods.list",
    "container.pods.portForward",
    "container.pods.proxy",
    "container.pods.update",
    "container.pods.updateStatus",
    "container.priorityClasses.create",
    "container.priorityClasses.delete",
    "container.priorityClasses.get",
    "container.priorityClasses.list",
    "container.priorityClasses.update",
    "container.replicaSets.create",
    "container.replicaSets.delete",
    "container.replicaSets.get",
    "container.replicaSets.getScale",
    "container.replicaSets.getStatus",
    "container.replicaSets.list",
    "container.replicaSets.update",
    "container.replicaSets.updateScale",
    "container.replicaSets.updateStatus",
    "container.replicationControllers.create",
    "container.replicationControllers.delete",
    "container.replicationControllers.get",
    "container.replicationControllers.getScale",
    "container.replicationControllers.getStatus",
    "container.replicationControllers.list",
    "container.replicationControllers.update",
    "container.replicationControllers.updateScale",
    "container.replicationControllers.updateStatus",
    "container.resourceQuotas.create",
    "container.resourceQuotas.delete",
    "container.resourceQuotas.get",
    "container.resourceQuotas.getStatus",
    "container.resourceQuotas.list",
    "container.resourceQuotas.update",
    "container.resourceQuotas.updateStatus",
    "container.roleBindings.create",
    "container.roleBindings.delete",
    "container.roleBindings.get",
    "container.roleBindings.list",
    "container.roleBindings.update",
    "container.roles.bind",
    "container.roles.create",
    "container.roles.delete",
    "container.roles.escalate",
    "container.roles.get",
    "container.roles.list",
    "container.roles.update",
    "container.runtimeClasses.create",
    "container.runtimeClasses.delete",
    "container.runtimeClasses.get",
    "container.runtimeClasses.list",
    "container.runtimeClasses.update",
    "container.scheduledJobs.create",
    "container.scheduledJobs.delete",
    "container.scheduledJobs.get",
    "container.scheduledJobs.list",
    "container.scheduledJobs.update",
    "container.scheduledJobs.updateStatus",
    "container.secrets.create",
    "container.secrets.delete",
    "container.secrets.get",
    "container.secrets.list",
    "container.secrets.update",
    "container.selfSubjectAccessReviews.create",
    "container.selfSubjectAccessReviews.list",
    "container.selfSubjectRulesReviews.create",
    "container.serviceAccounts.create",
    "container.serviceAccounts.createToken",
    "container.serviceAccounts.delete",
    "container.serviceAccounts.get",
    "container.serviceAccounts.list",
    "container.serviceAccounts.update",
    "container.services.create",
    "container.services.delete",
    "container.services.get",
    "container.services.getStatus",
    "container.services.list",
    "container.services.proxy",
    "container.services.update",
    "container.services.updateStatus",
    "container.statefulSets.create",
    "container.statefulSets.delete",
    "container.statefulSets.get",
    "container.statefulSets.getScale",
    "container.statefulSets.getStatus",
    "container.statefulSets.list",
    "container.statefulSets.update",
    "container.statefulSets.updateScale",
    "container.statefulSets.updateStatus",
    "container.storageClasses.create",
    "container.storageClasses.delete",
    "container.storageClasses.get",
    "container.storageClasses.list",
    "container.storageClasses.update",
    "container.storageStates.create",
    "container.storageStates.delete",
    "container.storageStates.get",
    "container.storageStates.getStatus",
    "container.storageStates.list",
    "container.storageStates.update",
    "container.storageStates.updateStatus",
    "container.storageVersionMigrations.create",
    "container.storageVersionMigrations.delete",
    "container.storageVersionMigrations.get",
    "container.storageVersionMigrations.getStatus",
    "container.storageVersionMigrations.list",
    "container.storageVersionMigrations.update",
    "container.storageVersionMigrations.updateStatus",
    "container.subjectAccessReviews.create",
    "container.subjectAccessReviews.list",
    "container.thirdPartyObjects.create",
    "container.thirdPartyObjects.delete",
    "container.thirdPartyObjects.get",
    "container.thirdPartyObjects.list",
    "container.thirdPartyObjects.update",
    "container.thirdPartyResources.create",
    "container.thirdPartyResources.delete",
    "container.thirdPartyResources.get",
    "container.thirdPartyResources.list",
    "container.thirdPartyResources.update",
    "container.tokenReviews.create",
    "container.updateInfos.create",
    "container.updateInfos.delete",
    "container.updateInfos.get",
    "container.updateInfos.list",
    "container.updateInfos.update",
    "container.validatingWebhookConfigurations.create",
    "container.validatingWebhookConfigurations.delete",
    "container.validatingWebhookConfigurations.get",
    "container.validatingWebhookConfigurations.list",
    "container.validatingWebhookConfigurations.update",
    "container.volumeAttachments.create",
    "container.volumeAttachments.delete",
    "container.volumeAttachments.get",
    "container.volumeAttachments.getStatus",
    "container.volumeAttachments.list",
    "container.volumeAttachments.update",
    "container.volumeAttachments.updateStatus",
    "container.volumeSnapshotClasses.create",
    "container.volumeSnapshotClasses.delete",
    "container.volumeSnapshotClasses.get",
    "container.volumeSnapshotClasses.list",
    "container.volumeSnapshotClasses.update",
    "container.volumeSnapshotContents.create",
    "container.volumeSnapshotContents.delete",
    "container.volumeSnapshotContents.get",
    "container.volumeSnapshotContents.getStatus",
    "container.volumeSnapshotContents.list",
    "container.volumeSnapshotContents.update",
    "container.volumeSnapshotContents.updateStatus",
    "container.volumeSnapshots.create",
    "container.volumeSnapshots.delete",
    "container.volumeSnapshots.get",
    "container.volumeSnapshots.getStatus",
    "container.volumeSnapshots.list",
    "container.volumeSnapshots.update",
    "container.volumeSnapshots.updateStatus",
    "containeranalysis.notes.attachOccurrence",
    "containeranalysis.notes.create",
    "containeranalysis.notes.delete",
    "containeranalysis.notes.get",
    "containeranalysis.notes.getIamPolicy",
    "containeranalysis.notes.list",
    "containeranalysis.notes.listOccurrences",
    "containeranalysis.notes.setIamPolicy",
    "containeranalysis.notes.update",
    "containeranalysis.occurrences.create",
    "containeranalysis.occurrences.delete",
    "containeranalysis.occurrences.get",
    "containeranalysis.occurrences.getIamPolicy",
    "containeranalysis.occurrences.list",
    "containeranalysis.occurrences.setIamPolicy",
    "containeranalysis.occurrences.update",
    "containersecurity.clusterSummaries.list",
    "containersecurity.findings.list",
    "containersecurity.locations.get",
    "containersecurity.locations.list",
    "containersecurity.workloadConfigAudits.list",
    "contentwarehouse.documentSchemas.create",
    "contentwarehouse.documentSchemas.delete",
    "contentwarehouse.documentSchemas.get",
    "contentwarehouse.documentSchemas.list",
    "contentwarehouse.documentSchemas.update",
    "contentwarehouse.documents.create",
    "contentwarehouse.documents.delete",
    "contentwarehouse.documents.get",
    "contentwarehouse.documents.getIamPolicy",
    "contentwarehouse.documents.setIamPolicy",
    "contentwarehouse.documents.update",
    "contentwarehouse.locations.initialize",
    "contentwarehouse.operations.get",
    "contentwarehouse.rawDocuments.download",
    "contentwarehouse.rawDocuments.upload",
    "contentwarehouse.ruleSets.create",
    "contentwarehouse.ruleSets.delete",
    "contentwarehouse.ruleSets.get",
    "contentwarehouse.ruleSets.list",
    "contentwarehouse.ruleSets.update",
    "contentwarehouse.synonymSets.create",
    "contentwarehouse.synonymSets.delete",
    "contentwarehouse.synonymSets.get",
    "contentwarehouse.synonymSets.list",
    "contentwarehouse.synonymSets.update",
    "datacatalog.categories.fineGrainedGet",
    "datacatalog.categories.getIamPolicy",
    "datacatalog.categories.setIamPolicy",
    "datacatalog.entries.create",
    "datacatalog.entries.delete",
    "datacatalog.entries.get",
    "datacatalog.entries.getIamPolicy",
    "datacatalog.entries.list",
    "datacatalog.entries.setIamPolicy",
    "datacatalog.entries.update",
    "datacatalog.entries.updateContacts",
    "datacatalog.entries.updateOverview",
    "datacatalog.entries.updateTag",
    "datacatalog.entryGroups.create",
    "datacatalog.entryGroups.delete",
    "datacatalog.entryGroups.get",
    "datacatalog.entryGroups.getIamPolicy",
    "datacatalog.entryGroups.list",
    "datacatalog.entryGroups.setIamPolicy",
    "datacatalog.entryGroups.update",
    "datacatalog.entryGroups.updateTag",
    "datacatalog.tagTemplates.create",
    "datacatalog.tagTemplates.delete",
    "datacatalog.tagTemplates.get",
    "datacatalog.tagTemplates.getIamPolicy",
    "datacatalog.tagTemplates.getTag",
    "datacatalog.tagTemplates.setIamPolicy",
    "datacatalog.tagTemplates.update",
    "datacatalog.tagTemplates.use",
    "datacatalog.taxonomies.create",
    "datacatalog.taxonomies.delete",
    "datacatalog.taxonomies.get",
    "datacatalog.taxonomies.getIamPolicy",
    "datacatalog.taxonomies.list",
    "datacatalog.taxonomies.setIamPolicy",
    "datacatalog.taxonomies.update",
    "dataconnectors.connectors.create",
    "dataconnectors.connectors.delete",
    "dataconnectors.connectors.get",
    "dataconnectors.connectors.getIamPolicy",
    "dataconnectors.connectors.list",
    "dataconnectors.connectors.setIamPolicy",
    "dataconnectors.connectors.update",
    "dataconnectors.connectors.use",
    "dataconnectors.locations.get",
    "dataconnectors.locations.list",
    "dataconnectors.operations.cancel",
    "dataconnectors.operations.delete",
    "dataconnectors.operations.get",
    "dataconnectors.operations.list",
    "dataflow.jobs.cancel",
    "dataflow.jobs.create",
    "dataflow.jobs.get",
    "dataflow.jobs.list",
    "dataflow.jobs.snapshot",
    "dataflow.jobs.updateContents",
    "dataflow.messages.list",
    "dataflow.metrics.get",
    "dataflow.shuffle.read",
    "dataflow.shuffle.write",
    "dataflow.snapshots.delete",
    "dataflow.snapshots.get",
    "dataflow.snapshots.list",
    "dataflow.streamingWorkItems.ImportState",
    "dataflow.streamingWorkItems.commitWork",
    "dataflow.streamingWorkItems.getData",
    "dataflow.streamingWorkItems.getWork",
    "dataflow.streamingWorkItems.getWorkerMetadata",
    "dataflow.workItems.lease",
    "dataflow.workItems.sendMessage",
    "dataflow.workItems.update",
    "dataform.compilationResults.create",
    "dataform.compilationResults.get",
    "dataform.compilationResults.list",
    "dataform.compilationResults.query",
    "dataform.locations.get",
    "dataform.locations.list",
    "dataform.repositories.create",
    "dataform.repositories.delete",
    "dataform.repositories.fetchRemoteBranches",
    "dataform.repositories.get",
    "dataform.repositories.list",
    "dataform.repositories.update",
    "dataform.workflowInvocations.cancel",
    "dataform.workflowInvocations.create",
    "dataform.workflowInvocations.delete",
    "dataform.workflowInvocations.get",
    "dataform.workflowInvocations.list",
    "dataform.workflowInvocations.query",
    "dataform.workspaces.commit",
    "dataform.workspaces.create",
    "dataform.workspaces.delete",
    "dataform.workspaces.fetchFileDiff",
    "dataform.workspaces.fetchFileGitStatuses",
    "dataform.workspaces.fetchGitAheadBehind",
    "dataform.workspaces.get",
    "dataform.workspaces.installNpmPackages",
    "dataform.workspaces.list",
    "dataform.workspaces.makeDirectory",
    "dataform.workspaces.moveDirectory",
    "dataform.workspaces.moveFile",
    "dataform.workspaces.pull",
    "dataform.workspaces.push",
    "dataform.workspaces.queryDirectoryContents",
    "dataform.workspaces.readFile",
    "dataform.workspaces.removeDirectory",
    "dataform.workspaces.removeFile",
    "dataform.workspaces.reset",
    "dataform.workspaces.writeFile",
    "datafusion.instances.create",
    "datafusion.instances.delete",
    "datafusion.instances.get",
    "datafusion.instances.getIamPolicy",
    "datafusion.instances.list",
    "datafusion.instances.restart",
    "datafusion.instances.runtime",
    "datafusion.instances.setIamPolicy",
    "datafusion.instances.update",
    "datafusion.instances.upgrade",
    "datafusion.locations.get",
    "datafusion.locations.list",
    "datafusion.operations.cancel",
    "datafusion.operations.delete",
    "datafusion.operations.get",
    "datafusion.operations.list",
    "datalabeling.annotateddatasets.delete",
    "datalabeling.annotateddatasets.get",
    "datalabeling.annotateddatasets.label",
    "datalabeling.annotateddatasets.list",
    "datalabeling.annotationspecsets.create",
    "datalabeling.annotationspecsets.delete",
    "datalabeling.annotationspecsets.get",
    "datalabeling.annotationspecsets.list",
    "datalabeling.dataitems.get",
    "datalabeling.dataitems.list",
    "datalabeling.datasets.create",
    "datalabeling.datasets.delete",
    "datalabeling.datasets.export",
    "datalabeling.datasets.get",
    "datalabeling.datasets.import",
    "datalabeling.datasets.list",
    "datalabeling.examples.get",
    "datalabeling.examples.list",
    "datalabeling.instructions.create",
    "datalabeling.instructions.delete",
    "datalabeling.instructions.get",
    "datalabeling.instructions.list",
    "datalabeling.operations.cancel",
    "datalabeling.operations.get",
    "datalabeling.operations.list",
    "datamigration.connectionprofiles.create",
    "datamigration.connectionprofiles.delete",
    "datamigration.connectionprofiles.get",
    "datamigration.connectionprofiles.getIamPolicy",
    "datamigration.connectionprofiles.list",
    "datamigration.connectionprofiles.setIamPolicy",
    "datamigration.connectionprofiles.update",
    "datamigration.locations.get",
    "datamigration.locations.list",
    "datamigration.migrationjobs.create",
    "datamigration.migrationjobs.delete",
    "datamigration.migrationjobs.generateSshScript",
    "datamigration.migrationjobs.get",
    "datamigration.migrationjobs.getIamPolicy",
    "datamigration.migrationjobs.list",
    "datamigration.migrationjobs.promote",
    "datamigration.migrationjobs.restart",
    "datamigration.migrationjobs.resume",
    "datamigration.migrationjobs.setIamPolicy",
    "datamigration.migrationjobs.start",
    "datamigration.migrationjobs.stop",
    "datamigration.migrationjobs.update",
    "datamigration.migrationjobs.verify",
    "datamigration.operations.cancel",
    "datamigration.operations.delete",
    "datamigration.operations.get",
    "datamigration.operations.list",
    "datapipelines.jobs.list",
    "datapipelines.pipelines.create",
    "datapipelines.pipelines.delete",
    "datapipelines.pipelines.get",
    "datapipelines.pipelines.list",
    "datapipelines.pipelines.run",
    "datapipelines.pipelines.stop",
    "datapipelines.pipelines.update",
    "dataplex.assetActions.list",
    "dataplex.assets.create",
    "dataplex.assets.delete",
    "dataplex.assets.get",
    "dataplex.assets.getIamPolicy",
    "dataplex.assets.list",
    "dataplex.assets.ownData",
    "dataplex.assets.readData",
    "dataplex.assets.setIamPolicy",
    "dataplex.assets.update",
    "dataplex.assets.writeData",
    "dataplex.content.create",
    "dataplex.content.delete",
    "dataplex.content.get",
    "dataplex.content.getIamPolicy",
    "dataplex.content.list",
    "dataplex.content.setIamPolicy",
    "dataplex.content.update",
    "dataplex.datascans.create",
    "dataplex.datascans.delete",
    "dataplex.datascans.get",
    "dataplex.datascans.getData",
    "dataplex.datascans.getIamPolicy",
    "dataplex.datascans.list",
    "dataplex.datascans.run",
    "dataplex.datascans.setIamPolicy",
    "dataplex.datascans.update",
    "dataplex.entities.create",
    "dataplex.entities.delete",
    "dataplex.entities.get",
    "dataplex.entities.list",
    "dataplex.entities.update",
    "dataplex.environments.create",
    "dataplex.environments.delete",
    "dataplex.environments.execute",
    "dataplex.environments.get",
    "dataplex.environments.getIamPolicy",
    "dataplex.environments.list",
    "dataplex.environments.setIamPolicy",
    "dataplex.environments.update",
    "dataplex.lakeActions.list",
    "dataplex.lakes.create",
    "dataplex.lakes.delete",
    "dataplex.lakes.get",
    "dataplex.lakes.getIamPolicy",
    "dataplex.lakes.list",
    "dataplex.lakes.setIamPolicy",
    "dataplex.lakes.update",
    "dataplex.locations.get",
    "dataplex.locations.list",
    "dataplex.operations.cancel",
    "dataplex.operations.delete",
    "dataplex.operations.get",
    "dataplex.operations.list",
    "dataplex.partitions.create",
    "dataplex.partitions.delete",
    "dataplex.partitions.get",
    "dataplex.partitions.list",
    "dataplex.partitions.update",
    "dataplex.tasks.cancel",
    "dataplex.tasks.create",
    "dataplex.tasks.delete",
    "dataplex.tasks.get",
    "dataplex.tasks.getIamPolicy",
    "dataplex.tasks.list",
    "dataplex.tasks.run",
    "dataplex.tasks.setIamPolicy",
    "dataplex.tasks.update",
    "dataplex.zoneActions.list",
    "dataplex.zones.create",
    "dataplex.zones.delete",
    "dataplex.zones.get",
    "dataplex.zones.getIamPolicy",
    "dataplex.zones.list",
    "dataplex.zones.setIamPolicy",
    "dataplex.zones.update",
    "dataprep.projects.use",
    "dataproc.agents.create",
    "dataproc.agents.delete",
    "dataproc.agents.get",
    "dataproc.agents.list",
    "dataproc.agents.update",
    "dataproc.autoscalingPolicies.create",
    "dataproc.autoscalingPolicies.delete",
    "dataproc.autoscalingPolicies.get",
    "dataproc.autoscalingPolicies.getIamPolicy",
    "dataproc.autoscalingPolicies.list",
    "dataproc.autoscalingPolicies.setIamPolicy",
    "dataproc.autoscalingPolicies.update",
    "dataproc.autoscalingPolicies.use",
    "dataproc.batches.cancel",
    "dataproc.batches.create",
    "dataproc.batches.delete",
    "dataproc.batches.get",
    "dataproc.batches.list",
    "dataproc.clusters.create",
    "dataproc.clusters.delete",
    "dataproc.clusters.get",
    "dataproc.clusters.getIamPolicy",
    "dataproc.clusters.list",
    "dataproc.clusters.setIamPolicy",
    "dataproc.clusters.start",
    "dataproc.clusters.stop",
    "dataproc.clusters.update",
    "dataproc.clusters.use",
    "dataproc.jobs.cancel",
    "dataproc.jobs.create",
    "dataproc.jobs.delete",
    "dataproc.jobs.get",
    "dataproc.jobs.getIamPolicy",
    "dataproc.jobs.list",
    "dataproc.jobs.setIamPolicy",
    "dataproc.jobs.update",
    "dataproc.operations.cancel",
    "dataproc.operations.delete",
    "dataproc.operations.get",
    "dataproc.operations.getIamPolicy",
    "dataproc.operations.list",
    "dataproc.operations.setIamPolicy",
    "dataproc.tasks.lease",
    "dataproc.tasks.listInvalidatedLeases",
    "dataproc.tasks.reportStatus",
    "dataproc.workflowTemplates.create",
    "dataproc.workflowTemplates.delete",
    "dataproc.workflowTemplates.get",
    "dataproc.workflowTemplates.getIamPolicy",
    "dataproc.workflowTemplates.instantiate",
    "dataproc.workflowTemplates.instantiateInline",
    "dataproc.workflowTemplates.list",
    "dataproc.workflowTemplates.setIamPolicy",
    "dataproc.workflowTemplates.update",
    "dataprocessing.datasources.get",
    "dataprocessing.datasources.list",
    "dataprocessing.datasources.update",
    "dataprocessing.featurecontrols.list",
    "dataprocessing.featurecontrols.update",
    "dataprocessing.groupcontrols.get",
    "dataprocessing.groupcontrols.list",
    "dataprocessing.groupcontrols.update",
    "datastore.databases.create",
    "datastore.databases.export",
    "datastore.databases.get",
    "datastore.databases.getMetadata",
    "datastore.databases.import",
    "datastore.databases.list",
    "datastore.databases.update",
    "datastore.entities.allocateIds",
    "datastore.entities.create",
    "datastore.entities.delete",
    "datastore.entities.get",
    "datastore.entities.list",
    "datastore.entities.update",
    "datastore.indexes.create",
    "datastore.indexes.delete",
    "datastore.indexes.get",
    "datastore.indexes.list",
    "datastore.indexes.update",
    "datastore.keyVisualizerScans.get",
    "datastore.keyVisualizerScans.list",
    "datastore.locations.get",
    "datastore.locations.list",
    "datastore.namespaces.get",
    "datastore.namespaces.list",
    "datastore.operations.cancel",
    "datastore.operations.delete",
    "datastore.operations.get",
    "datastore.operations.list",
    "datastore.statistics.get",
    "datastore.statistics.list",
    "datastream.connectionProfiles.create",
    "datastream.connectionProfiles.createTagBinding",
    "datastream.connectionProfiles.delete",
    "datastream.connectionProfiles.deleteTagBinding",
    "datastream.connectionProfiles.destinationTypes",
    "datastream.connectionProfiles.discover",
    "datastream.connectionProfiles.get",
    "datastream.connectionProfiles.getIamPolicy",
    "datastream.connectionProfiles.list",
    "datastream.connectionProfiles.listEffectiveTags",
    "datastream.connectionProfiles.listStaticServiceIps",
    "datastream.connectionProfiles.listTagBindings",
    "datastream.connectionProfiles.setIamPolicy",
    "datastream.connectionProfiles.sourceTypes",
    "datastream.connectionProfiles.update",
    "datastream.locations.fetchStaticIps",
    "datastream.locations.get",
    "datastream.locations.list",
    "datastream.objects.get",
    "datastream.objects.list",
    "datastream.objects.startBackfillJob",
    "datastream.objects.stopBackfillJob",
    "datastream.operations.cancel",
    "datastream.operations.delete",
    "datastream.operations.get",
    "datastream.operations.list",
    "datastream.privateConnections.create",
    "datastream.privateConnections.createTagBinding",
    "datastream.privateConnections.delete",
    "datastream.privateConnections.deleteTagBinding",
    "datastream.privateConnections.get",
    "datastream.privateConnections.getIamPolicy",
    "datastream.privateConnections.list",
    "datastream.privateConnections.listEffectiveTags",
    "datastream.privateConnections.listTagBindings",
    "datastream.privateConnections.setIamPolicy",
    "datastream.routes.create",
    "datastream.routes.delete",
    "datastream.routes.get",
    "datastream.routes.getIamPolicy",
    "datastream.routes.list",
    "datastream.routes.setIamPolicy",
    "datastream.streams.computeState",
    "datastream.streams.create",
    "datastream.streams.createTagBinding",
    "datastream.streams.delete",
    "datastream.streams.deleteTagBinding",
    "datastream.streams.fetchErrors",
    "datastream.streams.get",
    "datastream.streams.getIamPolicy",
    "datastream.streams.list",
    "datastream.streams.listEffectiveTags",
    "datastream.streams.listTagBindings",
    "datastream.streams.pause",
    "datastream.streams.resume",
    "datastream.streams.setIamPolicy",
    "datastream.streams.start",
    "datastream.streams.update",
    "datastudio.datasources.delete",
    "datastudio.datasources.get",
    "datastudio.datasources.getIamPolicy",
    "datastudio.datasources.move",
    "datastudio.datasources.restoreTrash",
    "datastudio.datasources.search",
    "datastudio.datasources.setIamPolicy",
    "datastudio.datasources.settingsShare",
    "datastudio.datasources.share",
    "datastudio.datasources.trash",
    "datastudio.datasources.update",
    "datastudio.reports.delete",
    "datastudio.reports.get",
    "datastudio.reports.getIamPolicy",
    "datastudio.reports.move",
    "datastudio.reports.restoreTrash",
    "datastudio.reports.search",
    "datastudio.reports.setIamPolicy",
    "datastudio.reports.settingsShare",
    "datastudio.reports.share",
    "datastudio.reports.trash",
    "datastudio.reports.update",
    "datastudio.workspaces.createUnder",
    "datastudio.workspaces.delete",
    "datastudio.workspaces.get",
    "datastudio.workspaces.getIamPolicy",
    "datastudio.workspaces.moveIn",
    "datastudio.workspaces.moveOut",
    "datastudio.workspaces.restoreTrash",
    "datastudio.workspaces.search",
    "datastudio.workspaces.setIamPolicy",
    "datastudio.workspaces.trash",
    "datastudio.workspaces.update",
    "deploymentmanager.compositeTypes.create",
    "deploymentmanager.compositeTypes.delete",
    "deploymentmanager.compositeTypes.get",
    "deploymentmanager.compositeTypes.list",
    "deploymentmanager.compositeTypes.update",
    "deploymentmanager.deployments.cancelPreview",
    "deploymentmanager.deployments.create",
    "deploymentmanager.deployments.delete",
    "deploymentmanager.deployments.get",
    "deploymentmanager.deployments.getIamPolicy",
    "deploymentmanager.deployments.list",
    "deploymentmanager.deployments.setIamPolicy",
    "deploymentmanager.deployments.stop",
    "deploymentmanager.deployments.update",
    "deploymentmanager.manifests.get",
    "deploymentmanager.manifests.list",
    "deploymentmanager.operations.get",
    "deploymentmanager.operations.list",
    "deploymentmanager.resources.get",
    "deploymentmanager.resources.list",
    "deploymentmanager.typeProviders.create",
    "deploymentmanager.typeProviders.delete",
    "deploymentmanager.typeProviders.get",
    "deploymentmanager.typeProviders.getType",
    "deploymentmanager.typeProviders.list",
    "deploymentmanager.typeProviders.listTypes",
    "deploymentmanager.typeProviders.update",
    "deploymentmanager.types.create",
    "deploymentmanager.types.delete",
    "deploymentmanager.types.get",
    "deploymentmanager.types.list",
    "deploymentmanager.types.update",
    "dialogflow.agents.create",
    "dialogflow.agents.delete",
    "dialogflow.agents.export",
    "dialogflow.agents.get",
    "dialogflow.agents.import",
    "dialogflow.agents.list",
    "dialogflow.agents.restore",
    "dialogflow.agents.search",
    "dialogflow.agents.searchResources",
    "dialogflow.agents.train",
    "dialogflow.agents.update",
    "dialogflow.agents.validate",
    "dialogflow.answerrecords.delete",
    "dialogflow.answerrecords.get",
    "dialogflow.answerrecords.list",
    "dialogflow.answerrecords.update",
    "dialogflow.callMatchers.create",
    "dialogflow.callMatchers.delete",
    "dialogflow.callMatchers.list",
    "dialogflow.changelogs.get",
    "dialogflow.changelogs.list",
    "dialogflow.contexts.create",
    "dialogflow.contexts.delete",
    "dialogflow.contexts.get",
    "dialogflow.contexts.list",
    "dialogflow.contexts.update",
    "dialogflow.conversationDatasets.create",
    "dialogflow.conversationDatasets.delete",
    "dialogflow.conversationDatasets.get",
    "dialogflow.conversationDatasets.import",
    "dialogflow.conversationDatasets.list",
    "dialogflow.conversationModels.create",
    "dialogflow.conversationModels.delete",
    "dialogflow.conversationModels.deploy",
    "dialogflow.conversationModels.get",
    "dialogflow.conversationModels.list",
    "dialogflow.conversationModels.undeploy",
    "dialogflow.conversationProfiles.create",
    "dialogflow.conversationProfiles.delete",
    "dialogflow.conversationProfiles.get",
    "dialogflow.conversationProfiles.list",
    "dialogflow.conversationProfiles.update",
    "dialogflow.conversations.addPhoneNumber",
    "dialogflow.conversations.complete",
    "dialogflow.conversations.create",
    "dialogflow.conversations.get",
    "dialogflow.conversations.list",
    "dialogflow.conversations.update",
    "dialogflow.documents.create",
    "dialogflow.documents.delete",
    "dialogflow.documents.get",
    "dialogflow.documents.list",
    "dialogflow.entityTypes.create",
    "dialogflow.entityTypes.createEntity",
    "dialogflow.entityTypes.delete",
    "dialogflow.entityTypes.deleteEntity",
    "dialogflow.entityTypes.get",
    "dialogflow.entityTypes.list",
    "dialogflow.entityTypes.update",
    "dialogflow.entityTypes.updateEntity",
    "dialogflow.environments.create",
    "dialogflow.environments.delete",
    "dialogflow.environments.get",
    "dialogflow.environments.getHistory",
    "dialogflow.environments.list",
    "dialogflow.environments.lookupHistory",
    "dialogflow.environments.update",
    "dialogflow.flows.create",
    "dialogflow.flows.delete",
    "dialogflow.flows.get",
    "dialogflow.flows.list",
    "dialogflow.flows.train",
    "dialogflow.flows.update",
    "dialogflow.flows.validate",
    "dialogflow.fulfillments.get",
    "dialogflow.fulfillments.update",
    "dialogflow.integrations.create",
    "dialogflow.integrations.delete",
    "dialogflow.integrations.get",
    "dialogflow.integrations.list",
    "dialogflow.integrations.update",
    "dialogflow.intents.create",
    "dialogflow.intents.delete",
    "dialogflow.intents.get",
    "dialogflow.intents.list",
    "dialogflow.intents.update",
    "dialogflow.knowledgeBases.create",
    "dialogflow.knowledgeBases.delete",
    "dialogflow.knowledgeBases.get",
    "dialogflow.knowledgeBases.list",
    "dialogflow.messages.list",
    "dialogflow.modelEvaluations.get",
    "dialogflow.modelEvaluations.list",
    "dialogflow.operations.get",
    "dialogflow.pages.create",
    "dialogflow.pages.delete",
    "dialogflow.pages.get",
    "dialogflow.pages.list",
    "dialogflow.pages.update",
    "dialogflow.participants.analyzeContent",
    "dialogflow.participants.create",
    "dialogflow.participants.get",
    "dialogflow.participants.list",
    "dialogflow.participants.suggest",
    "dialogflow.participants.update",
    "dialogflow.phoneNumberOrders.cancel",
    "dialogflow.phoneNumberOrders.create",
    "dialogflow.phoneNumberOrders.get",
    "dialogflow.phoneNumberOrders.list",
    "dialogflow.phoneNumberOrders.update",
    "dialogflow.phoneNumbers.delete",
    "dialogflow.phoneNumbers.list",
    "dialogflow.phoneNumbers.undelete",
    "dialogflow.phoneNumbers.update",
    "dialogflow.securitySettings.create",
    "dialogflow.securitySettings.delete",
    "dialogflow.securitySettings.get",
    "dialogflow.securitySettings.list",
    "dialogflow.securitySettings.update",
    "dialogflow.sessionEntityTypes.create",
    "dialogflow.sessionEntityTypes.delete",
    "dialogflow.sessionEntityTypes.get",
    "dialogflow.sessionEntityTypes.list",
    "dialogflow.sessionEntityTypes.update",
    "dialogflow.sessions.detectIntent",
    "dialogflow.sessions.streamingDetectIntent",
    "dialogflow.smartMessagingEntries.create",
    "dialogflow.smartMessagingEntries.delete",
    "dialogflow.smartMessagingEntries.get",
    "dialogflow.smartMessagingEntries.list",
    "dialogflow.transitionRouteGroups.create",
    "dialogflow.transitionRouteGroups.delete",
    "dialogflow.transitionRouteGroups.get",
    "dialogflow.transitionRouteGroups.list",
    "dialogflow.transitionRouteGroups.update",
    "dialogflow.versions.create",
    "dialogflow.versions.delete",
    "dialogflow.versions.get",
    "dialogflow.versions.list",
    "dialogflow.versions.load",
    "dialogflow.versions.update",
    "dialogflow.webhooks.create",
    "dialogflow.webhooks.delete",
    "dialogflow.webhooks.get",
    "dialogflow.webhooks.list",
    "dialogflow.webhooks.update",
    "discoveryengine.documents.create",
    "discoveryengine.documents.delete",
    "discoveryengine.documents.get",
    "discoveryengine.documents.import",
    "discoveryengine.documents.list",
    "discoveryengine.documents.update",
    "discoveryengine.operations.get",
    "discoveryengine.operations.list",
    "discoveryengine.servingConfigs.recommend",
    "discoveryengine.userEvents.create",
    "discoveryengine.userEvents.import",
    "dlp.analyzeRiskTemplates.create",
    "dlp.analyzeRiskTemplates.delete",
    "dlp.analyzeRiskTemplates.get",
    "dlp.analyzeRiskTemplates.list",
    "dlp.analyzeRiskTemplates.update",
    "dlp.columnDataProfiles.get",
    "dlp.columnDataProfiles.list",
    "dlp.deidentifyTemplates.create",
    "dlp.deidentifyTemplates.delete",
    "dlp.deidentifyTemplates.get",
    "dlp.deidentifyTemplates.list",
    "dlp.deidentifyTemplates.update",
    "dlp.estimates.cancel",
    "dlp.estimates.create",
    "dlp.estimates.delete",
    "dlp.estimates.get",
    "dlp.estimates.list",
    "dlp.inspectFindings.list",
    "dlp.inspectTemplates.create",
    "dlp.inspectTemplates.delete",
    "dlp.inspectTemplates.get",
    "dlp.inspectTemplates.list",
    "dlp.inspectTemplates.update",
    "dlp.jobTriggers.create",
    "dlp.jobTriggers.delete",
    "dlp.jobTriggers.get",
    "dlp.jobTriggers.hybridInspect",
    "dlp.jobTriggers.list",
    "dlp.jobTriggers.update",
    "dlp.jobs.cancel",
    "dlp.jobs.create",
    "dlp.jobs.delete",
    "dlp.jobs.get",
    "dlp.jobs.hybridInspect",
    "dlp.jobs.list",
    "dlp.kms.encrypt",
    "dlp.locations.get",
    "dlp.locations.list",
    "dlp.projectDataProfiles.get",
    "dlp.projectDataProfiles.list",
    "dlp.storedInfoTypes.create",
    "dlp.storedInfoTypes.delete",
    "dlp.storedInfoTypes.get",
    "dlp.storedInfoTypes.list",
    "dlp.storedInfoTypes.update",
    "dlp.tableDataProfiles.get",
    "dlp.tableDataProfiles.list",
    "dns.changes.create",
    "dns.changes.get",
    "dns.changes.list",
    "dns.dnsKeys.get",
    "dns.dnsKeys.list",
    "dns.managedZoneOperations.get",
    "dns.managedZoneOperations.list",
    "dns.managedZones.create",
    "dns.managedZones.delete",
    "dns.managedZones.get",
    "dns.managedZones.getIamPolicy",
    "dns.managedZones.list",
    "dns.managedZones.setIamPolicy",
    "dns.managedZones.update",
    "dns.networks.bindDNSResponsePolicy",
    "dns.networks.bindPrivateDNSPolicy",
    "dns.networks.bindPrivateDNSZone",
    "dns.networks.targetWithPeeringZone",
    "dns.policies.create",
    "dns.policies.delete",
    "dns.policies.get",
    "dns.policies.getIamPolicy",
    "dns.policies.list",
    "dns.policies.setIamPolicy",
    "dns.policies.update",
    "dns.projects.get",
    "dns.resourceRecordSets.create",
    "dns.resourceRecordSets.delete",
    "dns.resourceRecordSets.get",
    "dns.resourceRecordSets.list",
    "dns.resourceRecordSets.update",
    "dns.responsePolicies.create",
    "dns.responsePolicies.delete",
    "dns.responsePolicies.get",
    "dns.responsePolicies.list",
    "dns.responsePolicies.update",
    "dns.responsePolicyRules.create",
    "dns.responsePolicyRules.delete",
    "dns.responsePolicyRules.get",
    "dns.responsePolicyRules.list",
    "dns.responsePolicyRules.update",
    "documentai.dataLabelingJobs.cancel",
    "documentai.dataLabelingJobs.create",
    "documentai.dataLabelingJobs.delete",
    "documentai.dataLabelingJobs.list",
    "documentai.dataLabelingJobs.update",
    "documentai.datasetSchemas.get",
    "documentai.datasetSchemas.update",
    "documentai.datasets.createDocuments",
    "documentai.datasets.deleteDocuments",
    "documentai.datasets.get",
    "documentai.datasets.getDocuments",
    "documentai.datasets.listDocuments",
    "documentai.datasets.update",
    "documentai.datasets.updateDocuments",
    "documentai.evaluationDocuments.get",
    "documentai.evaluations.create",
    "documentai.evaluations.get",
    "documentai.evaluations.list",
    "documentai.humanReviewConfigs.get",
    "documentai.humanReviewConfigs.review",
    "documentai.humanReviewConfigs.update",
    "documentai.labelerPools.create",
    "documentai.labelerPools.delete",
    "documentai.labelerPools.get",
    "documentai.labelerPools.list",
    "documentai.labelerPools.update",
    "documentai.locations.get",
    "documentai.locations.list",
    "documentai.operations.getLegacy",
    "documentai.processedDocumentsSets.get",
    "documentai.processedDocumentsSets.getDocuments",
    "documentai.processedDocumentsSets.listDocuments",
    "documentai.processorTypes.get",
    "documentai.processorTypes.list",
    "documentai.processorVersions.create",
    "documentai.processorVersions.delete",
    "documentai.processorVersions.get",
    "documentai.processorVersions.list",
    "documentai.processorVersions.processBatch",
    "documentai.processorVersions.processOnline",
    "documentai.processorVersions.update",
    "documentai.processors.create",
    "documentai.processors.delete",
    "documentai.processors.fetchHumanReviewDetails",
    "documentai.processors.get",
    "documentai.processors.list",
    "documentai.processors.processBatch",
    "documentai.processors.processOnline",
    "documentai.processors.update",
    "domains.locations.get",
    "domains.locations.list",
    "domains.operations.cancel",
    "domains.operations.get",
    "domains.operations.list",
    "domains.registrations.configureContact",
    "domains.registrations.configureDns",
    "domains.registrations.configureManagement",
    "domains.registrations.create",
    "domains.registrations.createTagBinding",
    "domains.registrations.delete",
    "domains.registrations.deleteTagBinding",
    "domains.registrations.get",
    "domains.registrations.getIamPolicy",
    "domains.registrations.list",
    "domains.registrations.listEffectiveTags",
    "domains.registrations.listTagBindings",
    "domains.registrations.setIamPolicy",
    "domains.registrations.update",
    "earthengine.assets.create",
    "earthengine.assets.delete",
    "earthengine.assets.get",
    "earthengine.assets.getIamPolicy",
    "earthengine.assets.list",
    "earthengine.assets.setIamPolicy",
    "earthengine.assets.update",
    "earthengine.computations.create",
    "earthengine.config.get",
    "earthengine.config.update",
    "earthengine.exports.create",
    "earthengine.filmstripthumbnails.create",
    "earthengine.filmstripthumbnails.get",
    "earthengine.imports.create",
    "earthengine.maps.create",
    "earthengine.maps.get",
    "earthengine.operations.delete",
    "earthengine.operations.get",
    "earthengine.operations.list",
    "earthengine.operations.update",
    "earthengine.tables.create",
    "earthengine.tables.get",
    "earthengine.thumbnails.create",
    "earthengine.thumbnails.get",
    "earthengine.videothumbnails.create",
    "earthengine.videothumbnails.get",
    "edgecontainer.clusters.create",
    "edgecontainer.clusters.delete",
    "edgecontainer.clusters.generateAccessToken",
    "edgecontainer.clusters.get",
    "edgecontainer.clusters.getIamPolicy",
    "edgecontainer.clusters.list",
    "edgecontainer.clusters.setIamPolicy",
    "edgecontainer.clusters.update",
    "edgecontainer.locations.get",
    "edgecontainer.locations.list",
    "edgecontainer.machines.create",
    "edgecontainer.machines.delete",
    "edgecontainer.machines.get",
    "edgecontainer.machines.getIamPolicy",
    "edgecontainer.machines.list",
    "edgecontainer.machines.setIamPolicy",
    "edgecontainer.machines.update",
    "edgecontainer.machines.use",
    "edgecontainer.nodePools.create",
    "edgecontainer.nodePools.delete",
    "edgecontainer.nodePools.get",
    "edgecontainer.nodePools.getIamPolicy",
    "edgecontainer.nodePools.list",
    "edgecontainer.nodePools.setIamPolicy",
    "edgecontainer.nodePools.update",
    "edgecontainer.operations.cancel",
    "edgecontainer.operations.delete",
    "edgecontainer.operations.get",
    "edgecontainer.operations.list",
    "edgecontainer.vpnConnections.create",
    "edgecontainer.vpnConnections.delete",
    "edgecontainer.vpnConnections.get",
    "edgecontainer.vpnConnections.getIamPolicy",
    "edgecontainer.vpnConnections.list",
    "edgecontainer.vpnConnections.setIamPolicy",
    "edgecontainer.vpnConnections.update",
    "endpoints.portals.attachCustomDomain",
    "endpoints.portals.detachCustomDomain",
    "endpoints.portals.listCustomDomains",
    "endpoints.portals.update",
    "enterpriseknowledgegraph.cloudKnowledgeGraphEntities.lookup",
    "enterpriseknowledgegraph.cloudKnowledgeGraphEntities.search",
    "enterpriseknowledgegraph.entityReconciliationJobs.cancel",
    "enterpriseknowledgegraph.entityReconciliationJobs.create",
    "enterpriseknowledgegraph.entityReconciliationJobs.delete",
    "enterpriseknowledgegraph.entityReconciliationJobs.get",
    "enterpriseknowledgegraph.entityReconciliationJobs.list",
    "enterpriseknowledgegraph.publicKnowledgeGraphEntities.lookup",
    "enterpriseknowledgegraph.publicKnowledgeGraphEntities.search",
    "errorreporting.applications.list",
    "errorreporting.errorEvents.create",
    "errorreporting.errorEvents.delete",
    "errorreporting.errorEvents.list",
    "errorreporting.groupMetadata.get",
    "errorreporting.groupMetadata.update",
    "errorreporting.groups.list",
    "essentialcontacts.contacts.create",
    "essentialcontacts.contacts.delete",
    "essentialcontacts.contacts.get",
    "essentialcontacts.contacts.list",
    "essentialcontacts.contacts.send",
    "essentialcontacts.contacts.update",
    "eventarc.channelConnections.create",
    "eventarc.channelConnections.delete",
    "eventarc.channelConnections.get",
    "eventarc.channelConnections.getIamPolicy",
    "eventarc.channelConnections.list",
    "eventarc.channelConnections.publish",
    "eventarc.channelConnections.setIamPolicy",
    "eventarc.channels.attach",
    "eventarc.channels.create",
    "eventarc.channels.delete",
    "eventarc.channels.get",
    "eventarc.channels.getIamPolicy",
    "eventarc.channels.list",
    "eventarc.channels.publish",
    "eventarc.channels.setIamPolicy",
    "eventarc.channels.undelete",
    "eventarc.channels.update",
    "eventarc.events.receiveAuditLogWritten",
    "eventarc.events.receiveEvent",
    "eventarc.googleChannelConfigs.get",
    "eventarc.googleChannelConfigs.update",
    "eventarc.locations.get",
    "eventarc.locations.list",
    "eventarc.operations.cancel",
    "eventarc.operations.delete",
    "eventarc.operations.get",
    "eventarc.operations.list",
    "eventarc.providers.get",
    "eventarc.providers.list",
    "eventarc.triggers.create",
    "eventarc.triggers.delete",
    "eventarc.triggers.get",
    "eventarc.triggers.getIamPolicy",
    "eventarc.triggers.list",
    "eventarc.triggers.setIamPolicy",
    "eventarc.triggers.undelete",
    "eventarc.triggers.update",
    "fcmdata.deliverydata.list",
    "file.backups.create",
    "file.backups.createTagBinding",
    "file.backups.delete",
    "file.backups.deleteTagBinding",
    "file.backups.get",
    "file.backups.list",
    "file.backups.listEffectiveTags",
    "file.backups.listTagBindings",
    "file.backups.update",
    "file.instances.create",
    "file.instances.createTagBinding",
    "file.instances.delete",
    "file.instances.deleteTagBinding",
    "file.instances.get",
    "file.instances.list",
    "file.instances.listEffectiveTags",
    "file.instances.listTagBindings",
    "file.instances.restore",
    "file.instances.revert",
    "file.instances.update",
    "file.locations.get",
    "file.locations.list",
    "file.operations.cancel",
    "file.operations.delete",
    "file.operations.get",
    "file.operations.list",
    "file.snapshots.createTagBinding",
    "file.snapshots.deleteTagBinding",
    "file.snapshots.listEffectiveTags",
    "file.snapshots.listTagBindings",
    "firebase.billingPlans.get",
    "firebase.billingPlans.update",
    "firebase.clients.create",
    "firebase.clients.delete",
    "firebase.clients.get",
    "firebase.clients.list",
    "firebase.clients.undelete",
    "firebase.clients.update",
    "firebase.links.create",
    "firebase.links.delete",
    "firebase.links.list",
    "firebase.links.update",
    "firebase.playLinks.get",
    "firebase.playLinks.list",
    "firebase.playLinks.update",
    "firebase.projects.delete",
    "firebase.projects.get",
    "firebase.projects.update",
    "firebaseabt.experimentresults.get",
    "firebaseabt.experiments.create",
    "firebaseabt.experiments.delete",
    "firebaseabt.experiments.get",
    "firebaseabt.experiments.list",
    "firebaseabt.experiments.update",
    "firebaseabt.projectmetadata.get",
    "firebaseanalytics.resources.googleAnalyticsEdit",
    "firebaseanalytics.resources.googleAnalyticsReadAndAnalyze",
    "firebaseappcheck.appAttestConfig.get",
    "firebaseappcheck.appAttestConfig.update",
    "firebaseappcheck.debugTokens.get",
    "firebaseappcheck.debugTokens.update",
    "firebaseappcheck.deviceCheckConfig.get",
    "firebaseappcheck.deviceCheckConfig.update",
    "firebaseappcheck.playIntegrityConfig.get",
    "firebaseappcheck.playIntegrityConfig.update",
    "firebaseappcheck.recaptchaEnterpriseConfig.get",
    "firebaseappcheck.recaptchaEnterpriseConfig.update",
    "firebaseappcheck.recaptchaV3Config.get",
    "firebaseappcheck.recaptchaV3Config.update",
    "firebaseappcheck.safetyNetConfig.get",
    "firebaseappcheck.safetyNetConfig.update",
    "firebaseappcheck.services.get",
    "firebaseappcheck.services.update",
    "firebaseappdistro.groups.list",
    "firebaseappdistro.groups.update",
    "firebaseappdistro.releases.list",
    "firebaseappdistro.releases.update",
    "firebaseappdistro.testers.list",
    "firebaseappdistro.testers.update",
    "firebaseauth.configs.create",
    "firebaseauth.configs.get",
    "firebaseauth.configs.getHashConfig",
    "firebaseauth.configs.update",
    "firebaseauth.users.create",
    "firebaseauth.users.createSession",
    "firebaseauth.users.delete",
    "firebaseauth.users.get",
    "firebaseauth.users.sendEmail",
    "firebaseauth.users.update",
    "firebasecrash.issues.update",
    "firebasecrash.reports.get",
    "firebasecrashlytics.config.get",
    "firebasecrashlytics.config.update",
    "firebasecrashlytics.data.get",
    "firebasecrashlytics.issues.get",
    "firebasecrashlytics.issues.list",
    "firebasecrashlytics.issues.update",
    "firebasecrashlytics.sessions.get",
    "firebasedatabase.instances.create",
    "firebasedatabase.instances.delete",
    "firebasedatabase.instances.disable",
    "firebasedatabase.instances.get",
    "firebasedatabase.instances.list",
    "firebasedatabase.instances.reenable",
    "firebasedatabase.instances.undelete",
    "firebasedatabase.instances.update",
    "firebasedynamiclinks.destinations.list",
    "firebasedynamiclinks.destinations.update",
    "firebasedynamiclinks.domains.create",
    "firebasedynamiclinks.domains.delete",
    "firebasedynamiclinks.domains.get",
    "firebasedynamiclinks.domains.list",
    "firebasedynamiclinks.domains.update",
    "firebasedynamiclinks.links.create",
    "firebasedynamiclinks.links.get",
    "firebasedynamiclinks.links.list",
    "firebasedynamiclinks.links.update",
    "firebasedynamiclinks.stats.get",
    "firebaseextensions.configs.create",
    "firebaseextensions.configs.delete",
    "firebaseextensions.configs.list",
    "firebaseextensions.configs.update",
    "firebasehosting.sites.create",
    "firebasehosting.sites.delete",
    "firebasehosting.sites.get",
    "firebasehosting.sites.list",
    "firebasehosting.sites.update",
    "firebaseinappmessaging.campaigns.create",
    "firebaseinappmessaging.campaigns.delete",
    "firebaseinappmessaging.campaigns.get",
    "firebaseinappmessaging.campaigns.list",
    "firebaseinappmessaging.campaigns.update",
    "firebasemessagingcampaigns.campaigns.create",
    "firebasemessagingcampaigns.campaigns.delete",
    "firebasemessagingcampaigns.campaigns.get",
    "firebasemessagingcampaigns.campaigns.list",
    "firebasemessagingcampaigns.campaigns.start",
    "firebasemessagingcampaigns.campaigns.stop",
    "firebasemessagingcampaigns.campaigns.update",
    "firebaseml.compressionjobs.create",
    "firebaseml.compressionjobs.delete",
    "firebaseml.compressionjobs.get",
    "firebaseml.compressionjobs.list",
    "firebaseml.compressionjobs.start",
    "firebaseml.compressionjobs.update",
    "firebaseml.models.create",
    "firebaseml.models.delete",
    "firebaseml.models.get",
    "firebaseml.models.list",
    "firebaseml.modelversions.create",
    "firebaseml.modelversions.get",
    "firebaseml.modelversions.list",
    "firebaseml.modelversions.update",
    "firebasenotifications.messages.create",
    "firebasenotifications.messages.delete",
    "firebasenotifications.messages.get",
    "firebasenotifications.messages.list",
    "firebasenotifications.messages.update",
    "firebaseperformance.config.update",
    "firebaseperformance.data.get",
    "firebaserules.releases.create",
    "firebaserules.releases.delete",
    "firebaserules.releases.get",
    "firebaserules.releases.getExecutable",
    "firebaserules.releases.list",
    "firebaserules.releases.update",
    "firebaserules.rulesets.create",
    "firebaserules.rulesets.delete",
    "firebaserules.rulesets.get",
    "firebaserules.rulesets.list",
    "firebaserules.rulesets.test",
    "firebasestorage.buckets.addFirebase",
    "firebasestorage.buckets.get",
    "firebasestorage.buckets.list",
    "firebasestorage.buckets.removeFirebase",
    "fleetengine.deliveryvehicles.create",
    "fleetengine.deliveryvehicles.get",
    "fleetengine.deliveryvehicles.list",
    "fleetengine.deliveryvehicles.update",
    "fleetengine.deliveryvehicles.updateLocation",
    "fleetengine.deliveryvehicles.updateVehicleStops",
    "fleetengine.tasks.create",
    "fleetengine.tasks.get",
    "fleetengine.tasks.list",
    "fleetengine.tasks.searchWithTrackingId",
    "fleetengine.tasks.update",
    "fleetengine.trips.create",
    "fleetengine.trips.get",
    "fleetengine.trips.search",
    "fleetengine.trips.update",
    "fleetengine.trips.updateState",
    "fleetengine.vehicles.create",
    "fleetengine.vehicles.get",
    "fleetengine.vehicles.list",
    "fleetengine.vehicles.search",
    "fleetengine.vehicles.searchFuzzed",
    "fleetengine.vehicles.update",
    "fleetengine.vehicles.updateLocation",
    "gameservices.gameServerClusters.create",
    "gameservices.gameServerClusters.delete",
    "gameservices.gameServerClusters.get",
    "gameservices.gameServerClusters.list",
    "gameservices.gameServerClusters.update",
    "gameservices.gameServerConfigs.create",
    "gameservices.gameServerConfigs.delete",
    "gameservices.gameServerConfigs.get",
    "gameservices.gameServerConfigs.list",
    "gameservices.gameServerDeployments.create",
    "gameservices.gameServerDeployments.delete",
    "gameservices.gameServerDeployments.get",
    "gameservices.gameServerDeployments.list",
    "gameservices.gameServerDeployments.rollout",
    "gameservices.gameServerDeployments.update",
    "gameservices.locations.get",
    "gameservices.locations.list",
    "gameservices.operations.cancel",
    "gameservices.operations.delete",
    "gameservices.operations.get",
    "gameservices.operations.list",
    "gameservices.realms.create",
    "gameservices.realms.delete",
    "gameservices.realms.get",
    "gameservices.realms.list",
    "gameservices.realms.update",
    "genomics.datasets.create",
    "genomics.datasets.delete",
    "genomics.datasets.get",
    "genomics.datasets.getIamPolicy",
    "genomics.datasets.list",
    "genomics.datasets.setIamPolicy",
    "genomics.datasets.update",
    "genomics.operations.cancel",
    "genomics.operations.create",
    "genomics.operations.get",
    "genomics.operations.list",
    "gkebackup.backupPlans.create",
    "gkebackup.backupPlans.delete",
    "gkebackup.backupPlans.get",
    "gkebackup.backupPlans.getIamPolicy",
    "gkebackup.backupPlans.list",
    "gkebackup.backupPlans.setIamPolicy",
    "gkebackup.backupPlans.update",
    "gkebackup.backups.create",
    "gkebackup.backups.delete",
    "gkebackup.backups.get",
    "gkebackup.backups.list",
    "gkebackup.backups.update",
    "gkebackup.locations.get",
    "gkebackup.locations.list",
    "gkebackup.operations.cancel",
    "gkebackup.operations.delete",
    "gkebackup.operations.get",
    "gkebackup.operations.list",
    "gkebackup.restorePlans.create",
    "gkebackup.restorePlans.delete",
    "gkebackup.restorePlans.get",
    "gkebackup.restorePlans.getIamPolicy",
    "gkebackup.restorePlans.list",
    "gkebackup.restorePlans.setIamPolicy",
    "gkebackup.restorePlans.update",
    "gkebackup.restores.create",
    "gkebackup.restores.delete",
    "gkebackup.restores.get",
    "gkebackup.restores.list",
    "gkebackup.restores.update",
    "gkebackup.volumeBackups.get",
    "gkebackup.volumeBackups.list",
    "gkebackup.volumeRestores.get",
    "gkebackup.volumeRestores.list",
    "gkehub.endpoints.connect",
    "gkehub.features.create",
    "gkehub.features.delete",
    "gkehub.features.get",
    "gkehub.features.getIamPolicy",
    "gkehub.features.list",
    "gkehub.features.setIamPolicy",
    "gkehub.features.update",
    "gkehub.fleet.create",
    "gkehub.fleet.delete",
    "gkehub.fleet.get",
    "gkehub.fleet.update",
    "gkehub.gateway.delete",
    "gkehub.gateway.get",
    "gkehub.gateway.getIamPolicy",
    "gkehub.gateway.patch",
    "gkehub.gateway.post",
    "gkehub.gateway.put",
    "gkehub.gateway.setIamPolicy",
    "gkehub.locations.get",
    "gkehub.locations.list",
    "gkehub.memberships.create",
    "gkehub.memberships.delete",
    "gkehub.memberships.generateConnectManifest",
    "gkehub.memberships.get",
    "gkehub.memberships.getIamPolicy",
    "gkehub.memberships.list",
    "gkehub.memberships.setIamPolicy",
    "gkehub.memberships.update",
    "gkehub.operations.cancel",
    "gkehub.operations.delete",
    "gkehub.operations.get",
    "gkehub.operations.list",
    "gkemulticloud.awsClusters.create",
    "gkemulticloud.awsClusters.delete",
    "gkemulticloud.awsClusters.generateAccessToken",
    "gkemulticloud.awsClusters.get",
    "gkemulticloud.awsClusters.getAdminKubeconfig",
    "gkemulticloud.awsClusters.list",
    "gkemulticloud.awsClusters.update",
    "gkemulticloud.awsNodePools.create",
    "gkemulticloud.awsNodePools.delete",
    "gkemulticloud.awsNodePools.get",
    "gkemulticloud.awsNodePools.list",
    "gkemulticloud.awsNodePools.update",
    "gkemulticloud.awsServerConfigs.get",
    "gkemulticloud.azureClients.create",
    "gkemulticloud.azureClients.delete",
    "gkemulticloud.azureClients.get",
    "gkemulticloud.azureClients.list",
    "gkemulticloud.azureClusters.create",
    "gkemulticloud.azureClusters.delete",
    "gkemulticloud.azureClusters.generateAccessToken",
    "gkemulticloud.azureClusters.get",
    "gkemulticloud.azureClusters.getAdminKubeconfig",
    "gkemulticloud.azureClusters.list",
    "gkemulticloud.azureClusters.update",
    "gkemulticloud.azureNodePools.create",
    "gkemulticloud.azureNodePools.delete",
    "gkemulticloud.azureNodePools.get",
    "gkemulticloud.azureNodePools.list",
    "gkemulticloud.azureNodePools.update",
    "gkemulticloud.azureServerConfigs.get",
    "gkemulticloud.operations.cancel",
    "gkemulticloud.operations.delete",
    "gkemulticloud.operations.get",
    "gkemulticloud.operations.list",
    "gkemulticloud.operations.wait",
    "gkeonprem.bareMetalAdminClusters.create",
    "gkeonprem.bareMetalAdminClusters.enroll",
    "gkeonprem.bareMetalAdminClusters.get",
    "gkeonprem.bareMetalAdminClusters.getIamPolicy",
    "gkeonprem.bareMetalAdminClusters.list",
    "gkeonprem.bareMetalAdminClusters.queryVersionConfig",
    "gkeonprem.bareMetalAdminClusters.setIamPolicy",
    "gkeonprem.bareMetalAdminClusters.unenroll",
    "gkeonprem.bareMetalAdminClusters.update",
    "gkeonprem.bareMetalClusters.create",
    "gkeonprem.bareMetalClusters.delete",
    "gkeonprem.bareMetalClusters.enroll",
    "gkeonprem.bareMetalClusters.get",
    "gkeonprem.bareMetalClusters.getIamPolicy",
    "gkeonprem.bareMetalClusters.list",
    "gkeonprem.bareMetalClusters.queryVersionConfig",
    "gkeonprem.bareMetalClusters.setIamPolicy",
    "gkeonprem.bareMetalClusters.unenroll",
    "gkeonprem.bareMetalClusters.update",
    "gkeonprem.bareMetalNodePools.create",
    "gkeonprem.bareMetalNodePools.delete",
    "gkeonprem.bareMetalNodePools.get",
    "gkeonprem.bareMetalNodePools.getIamPolicy",
    "gkeonprem.bareMetalNodePools.list",
    "gkeonprem.bareMetalNodePools.setIamPolicy",
    "gkeonprem.bareMetalNodePools.update",
    "gkeonprem.locations.get",
    "gkeonprem.locations.list",
    "gkeonprem.operations.cancel",
    "gkeonprem.operations.delete",
    "gkeonprem.operations.get",
    "gkeonprem.operations.list",
    "gkeonprem.vmwareAdminClusters.enroll",
    "gkeonprem.vmwareAdminClusters.get",
    "gkeonprem.vmwareAdminClusters.getIamPolicy",
    "gkeonprem.vmwareAdminClusters.list",
    "gkeonprem.vmwareAdminClusters.setIamPolicy",
    "gkeonprem.vmwareAdminClusters.unenroll",
    "gkeonprem.vmwareAdminClusters.update",
    "gkeonprem.vmwareClusters.create",
    "gkeonprem.vmwareClusters.delete",
    "gkeonprem.vmwareClusters.enroll",
    "gkeonprem.vmwareClusters.get",
    "gkeonprem.vmwareClusters.getIamPolicy",
    "gkeonprem.vmwareClusters.list",
    "gkeonprem.vmwareClusters.queryVersionConfig",
    "gkeonprem.vmwareClusters.setIamPolicy",
    "gkeonprem.vmwareClusters.unenroll",
    "gkeonprem.vmwareClusters.update",
    "gkeonprem.vmwareNodePools.create",
    "gkeonprem.vmwareNodePools.delete",
    "gkeonprem.vmwareNodePools.get",
    "gkeonprem.vmwareNodePools.getIamPolicy",
    "gkeonprem.vmwareNodePools.list",
    "gkeonprem.vmwareNodePools.setIamPolicy",
    "gkeonprem.vmwareNodePools.update",
    "gsuiteaddons.authorizations.get",
    "gsuiteaddons.deployments.create",
    "gsuiteaddons.deployments.delete",
    "gsuiteaddons.deployments.execute",
    "gsuiteaddons.deployments.get",
    "gsuiteaddons.deployments.install",
    "gsuiteaddons.deployments.installStatus",
    "gsuiteaddons.deployments.list",
    "gsuiteaddons.deployments.uninstall",
    "gsuiteaddons.deployments.update",
    "healthcare.annotationStores.create",
    "healthcare.annotationStores.delete",
    "healthcare.annotationStores.evaluate",
    "healthcare.annotationStores.export",
    "healthcare.annotationStores.get",
    "healthcare.annotationStores.getIamPolicy",
    "healthcare.annotationStores.import",
    "healthcare.annotationStores.list",
    "healthcare.annotationStores.setIamPolicy",
    "healthcare.annotationStores.update",
    "healthcare.annotations.create",
    "healthcare.annotations.delete",
    "healthcare.annotations.get",
    "healthcare.annotations.list",
    "healthcare.annotations.update",
    "healthcare.attributeDefinitions.create",
    "healthcare.attributeDefinitions.delete",
    "healthcare.attributeDefinitions.get",
    "healthcare.attributeDefinitions.list",
    "healthcare.attributeDefinitions.update",
    "healthcare.consentArtifacts.create",
    "healthcare.consentArtifacts.delete",
    "healthcare.consentArtifacts.get",
    "healthcare.consentArtifacts.list",
    "healthcare.consentStores.checkDataAccess",
    "healthcare.consentStores.create",
    "healthcare.consentStores.delete",
    "healthcare.consentStores.evaluateUserConsents",
    "healthcare.consentStores.get",
    "healthcare.consentStores.getIamPolicy",
    "healthcare.consentStores.list",
    "healthcare.consentStores.queryAccessibleData",
    "healthcare.consentStores.setIamPolicy",
    "healthcare.consentStores.update",
    "healthcare.consents.activate",
    "healthcare.consents.create",
    "healthcare.consents.delete",
    "healthcare.consents.get",
    "healthcare.consents.list",
    "healthcare.consents.reject",
    "healthcare.consents.revoke",
    "healthcare.consents.update",
    "healthcare.datasets.create",
    "healthcare.datasets.deidentify",
    "healthcare.datasets.delete",
    "healthcare.datasets.get",
    "healthcare.datasets.getIamPolicy",
    "healthcare.datasets.list",
    "healthcare.datasets.setIamPolicy",
    "healthcare.datasets.update",
    "healthcare.dicomStores.create",
    "healthcare.dicomStores.deidentify",
    "healthcare.dicomStores.delete",
    "healthcare.dicomStores.dicomWebDelete",
    "healthcare.dicomStores.dicomWebRead",
    "healthcare.dicomStores.dicomWebWrite",
    "healthcare.dicomStores.export",
    "healthcare.dicomStores.get",
    "healthcare.dicomStores.getIamPolicy",
    "healthcare.dicomStores.import",
    "healthcare.dicomStores.list",
    "healthcare.dicomStores.setIamPolicy",
    "healthcare.dicomStores.update",
    "healthcare.fhirResources.create",
    "healthcare.fhirResources.delete",
    "healthcare.fhirResources.get",
    "healthcare.fhirResources.patch",
    "healthcare.fhirResources.purge",
    "healthcare.fhirResources.translateConceptMap",
    "healthcare.fhirResources.update",
    "healthcare.fhirStores.configureSearch",
    "healthcare.fhirStores.create",
    "healthcare.fhirStores.deidentify",
    "healthcare.fhirStores.delete",
    "healthcare.fhirStores.executeBundle",
    "healthcare.fhirStores.export",
    "healthcare.fhirStores.get",
    "healthcare.fhirStores.getIamPolicy",
    "healthcare.fhirStores.import",
    "healthcare.fhirStores.list",
    "healthcare.fhirStores.searchResources",
    "healthcare.fhirStores.setIamPolicy",
    "healthcare.fhirStores.update",
    "healthcare.hl7V2Messages.create",
    "healthcare.hl7V2Messages.delete",
    "healthcare.hl7V2Messages.get",
    "healthcare.hl7V2Messages.ingest",
    "healthcare.hl7V2Messages.list",
    "healthcare.hl7V2Messages.update",
    "healthcare.hl7V2Stores.create",
    "healthcare.hl7V2Stores.delete",
    "healthcare.hl7V2Stores.get",
    "healthcare.hl7V2Stores.getIamPolicy",
    "healthcare.hl7V2Stores.import",
    "healthcare.hl7V2Stores.list",
    "healthcare.hl7V2Stores.setIamPolicy",
    "healthcare.hl7V2Stores.update",
    "healthcare.locations.get",
    "healthcare.locations.list",
    "healthcare.nlpservice.analyzeEntities",
    "healthcare.operations.cancel",
    "healthcare.operations.get",
    "healthcare.operations.list",
    "healthcare.userDataMappings.archive",
    "healthcare.userDataMappings.create",
    "healthcare.userDataMappings.delete",
    "healthcare.userDataMappings.get",
    "healthcare.userDataMappings.list",
    "healthcare.userDataMappings.update",
    "iam.denypolicies.create",
    "iam.denypolicies.delete",
    "iam.denypolicies.get",
    "iam.denypolicies.list",
    "iam.denypolicies.update",
    "iam.roles.create",
    "iam.roles.delete",
    "iam.roles.get",
    "iam.roles.list",
    "iam.roles.undelete",
    "iam.roles.update",
    "iam.serviceAccountKeys.create",
    "iam.serviceAccountKeys.delete",
    "iam.serviceAccountKeys.disable",
    "iam.serviceAccountKeys.enable",
    "iam.serviceAccountKeys.get",
    "iam.serviceAccountKeys.list",
    "iam.serviceAccounts.actAs",
    "iam.serviceAccounts.create",
    "iam.serviceAccounts.delete",
    "iam.serviceAccounts.disable",
    "iam.serviceAccounts.enable",
    "iam.serviceAccounts.get",
    "iam.serviceAccounts.getAccessToken",
    "iam.serviceAccounts.getIamPolicy",
    "iam.serviceAccounts.getOpenIdToken",
    "iam.serviceAccounts.implicitDelegation",
    "iam.serviceAccounts.list",
    "iam.serviceAccounts.setIamPolicy",
    "iam.serviceAccounts.signBlob",
    "iam.serviceAccounts.signJwt",
    "iam.serviceAccounts.undelete",
    "iam.serviceAccounts.update",
    "iap.projects.getSettings",
    "iap.projects.updateSettings",
    "iap.tunnel.getIamPolicy",
    "iap.tunnel.setIamPolicy",
    "iap.tunnelDestGroups.accessViaIAP",
    "iap.tunnelDestGroups.create",
    "iap.tunnelDestGroups.delete",
    "iap.tunnelDestGroups.get",
    "iap.tunnelDestGroups.getIamPolicy",
    "iap.tunnelDestGroups.list",
    "iap.tunnelDestGroups.setIamPolicy",
    "iap.tunnelDestGroups.update",
    "iap.tunnelInstances.accessViaIAP",
    "iap.tunnelInstances.getIamPolicy",
    "iap.tunnelInstances.setIamPolicy",
    "iap.tunnelLocations.getIamPolicy",
    "iap.tunnelLocations.setIamPolicy",
    "iap.tunnelZones.getIamPolicy",
    "iap.tunnelZones.setIamPolicy",
    "iap.web.getIamPolicy",
    "iap.web.getSettings",
    "iap.web.setIamPolicy",
    "iap.web.updateSettings",
    "iap.webServiceVersions.accessViaIAP",
    "iap.webServiceVersions.getIamPolicy",
    "iap.webServiceVersions.getSettings",
    "iap.webServiceVersions.setIamPolicy",
    "iap.webServiceVersions.updateSettings",
    "iap.webServices.getIamPolicy",
    "iap.webServices.getSettings",
    "iap.webServices.setIamPolicy",
    "iap.webServices.updateSettings",
    "iap.webTypes.getIamPolicy",
    "iap.webTypes.getSettings",
    "iap.webTypes.setIamPolicy",
    "iap.webTypes.updateSettings",
    "identitytoolkit.tenants.create",
    "identitytoolkit.tenants.delete",
    "identitytoolkit.tenants.get",
    "identitytoolkit.tenants.getIamPolicy",
    "identitytoolkit.tenants.list",
    "identitytoolkit.tenants.setIamPolicy",
    "identitytoolkit.tenants.update",
    "ids.endpoints.create",
    "ids.endpoints.delete",
    "ids.endpoints.get",
    "ids.endpoints.getIamPolicy",
    "ids.endpoints.list",
    "ids.endpoints.setIamPolicy",
    "ids.endpoints.update",
    "ids.locations.get",
    "ids.locations.list",
    "ids.operations.cancel",
    "ids.operations.delete",
    "ids.operations.get",
    "ids.operations.list",
    "integrations.apigeeAuthConfigs.create",
    "integrations.apigeeAuthConfigs.delete",
    "integrations.apigeeAuthConfigs.get",
    "integrations.apigeeAuthConfigs.list",
    "integrations.apigeeAuthConfigs.update",
    "integrations.apigeeCertificates.create",
    "integrations.apigeeCertificates.delete",
    "integrations.apigeeCertificates.get",
    "integrations.apigeeCertificates.list",
    "integrations.apigeeCertificates.update",
    "integrations.apigeeExecutions.list",
    "integrations.apigeeIntegrationVers.create",
    "integrations.apigeeIntegrationVers.delete",
    "integrations.apigeeIntegrationVers.deploy",
    "integrations.apigeeIntegrationVers.get",
    "integrations.apigeeIntegrationVers.list",
    "integrations.apigeeIntegrationVers.update",
    "integrations.apigeeIntegrations.invoke",
    "integrations.apigeeIntegrations.list",
    "integrations.apigeeSfdcChannels.create",
    "integrations.apigeeSfdcChannels.delete",
    "integrations.apigeeSfdcChannels.get",
    "integrations.apigeeSfdcChannels.list",
    "integrations.apigeeSfdcChannels.update",
    "integrations.apigeeSfdcInstances.create",
    "integrations.apigeeSfdcInstances.delete",
    "integrations.apigeeSfdcInstances.get",
    "integrations.apigeeSfdcInstances.list",
    "integrations.apigeeSfdcInstances.update",
    "integrations.apigeeSuspensions.lift",
    "integrations.apigeeSuspensions.list",
    "integrations.apigeeSuspensions.resolve",
    "integrations.authConfigs.create",
    "integrations.authConfigs.delete",
    "integrations.authConfigs.get",
    "integrations.authConfigs.list",
    "integrations.authConfigs.update",
    "integrations.certificates.create",
    "integrations.certificates.delete",
    "integrations.certificates.get",
    "integrations.certificates.list",
    "integrations.certificates.update",
    "integrations.executions.get",
    "integrations.executions.list",
    "integrations.integrationVersions.create",
    "integrations.integrationVersions.delete",
    "integrations.integrationVersions.deploy",
    "integrations.integrationVersions.get",
    "integrations.integrationVersions.invoke",
    "integrations.integrationVersions.list",
    "integrations.integrationVersions.update",
    "integrations.integrations.create",
    "integrations.integrations.delete",
    "integrations.integrations.deploy",
    "integrations.integrations.get",
    "integrations.integrations.invoke",
    "integrations.integrations.list",
    "integrations.integrations.update",
    "integrations.securityAuthConfigs.create",
    "integrations.securityAuthConfigs.delete",
    "integrations.securityAuthConfigs.get",
    "integrations.securityAuthConfigs.list",
    "integrations.securityAuthConfigs.update",
    "integrations.securityExecutions.cancel",
    "integrations.securityExecutions.get",
    "integrations.securityExecutions.list",
    "integrations.securityIntegTempVers.create",
    "integrations.securityIntegTempVers.get",
    "integrations.securityIntegTempVers.list",
    "integrations.securityIntegrationVers.create",
    "integrations.securityIntegrationVers.deploy",
    "integrations.securityIntegrationVers.get",
    "integrations.securityIntegrationVers.list",
    "integrations.securityIntegrationVers.update",
    "integrations.securityIntegrations.invoke",
    "integrations.securityIntegrations.list",
    "integrations.sfdcChannels.create",
    "integrations.sfdcChannels.delete",
    "integrations.sfdcChannels.get",
    "integrations.sfdcChannels.list",
    "integrations.sfdcChannels.update",
    "integrations.sfdcInstances.create",
    "integrations.sfdcInstances.delete",
    "integrations.sfdcInstances.get",
    "integrations.sfdcInstances.list",
    "integrations.sfdcInstances.update",
    "integrations.suspensions.lift",
    "integrations.suspensions.list",
    "integrations.suspensions.resolve",
    "issuerswitch.complaintTransactions.list",
    "issuerswitch.complaints.create",
    "issuerswitch.complaints.resolve",
    "issuerswitch.disputes.create",
    "issuerswitch.disputes.resolve",
    "issuerswitch.financialTransactions.list",
    "issuerswitch.mandateTransactions.list",
    "issuerswitch.metadataTransactions.list",
    "issuerswitch.operations.cancel",
    "issuerswitch.operations.delete",
    "issuerswitch.operations.get",
    "issuerswitch.operations.list",
    "issuerswitch.operations.wait",
    "issuerswitch.ruleMetadata.list",
    "issuerswitch.ruleMetadataValues.create",
    "issuerswitch.ruleMetadataValues.delete",
    "issuerswitch.ruleMetadataValues.list",
    "issuerswitch.rules.list",
    "krmapihosting.krmApiHosts.create",
    "krmapihosting.krmApiHosts.delete",
    "krmapihosting.krmApiHosts.get",
    "krmapihosting.krmApiHosts.getIamPolicy",
    "krmapihosting.krmApiHosts.list",
    "krmapihosting.krmApiHosts.setIamPolicy",
    "krmapihosting.krmApiHosts.update",
    "krmapihosting.locations.get",
    "krmapihosting.locations.list",
    "krmapihosting.operations.cancel",
    "krmapihosting.operations.delete",
    "krmapihosting.operations.get",
    "krmapihosting.operations.list",
    "lifesciences.operations.cancel",
    "lifesciences.operations.get",
    "lifesciences.operations.list",
    "lifesciences.workflows.run",
    "livestream.channels.create",
    "livestream.channels.delete",
    "livestream.channels.get",
    "livestream.channels.list",
    "livestream.channels.start",
    "livestream.channels.stop",
    "livestream.channels.update",
    "livestream.events.create",
    "livestream.events.delete",
    "livestream.events.get",
    "livestream.events.list",
    "livestream.inputs.create",
    "livestream.inputs.delete",
    "livestream.inputs.get",
    "livestream.inputs.list",
    "livestream.inputs.update",
    "livestream.locations.get",
    "livestream.locations.list",
    "livestream.operations.cancel",
    "livestream.operations.delete",
    "livestream.operations.get",
    "livestream.operations.list",
    "logging.buckets.copyLogEntries",
    "logging.buckets.create",
    "logging.buckets.delete",
    "logging.buckets.get",
    "logging.buckets.list",
    "logging.buckets.undelete",
    "logging.buckets.update",
    "logging.buckets.write",
    "logging.exclusions.create",
    "logging.exclusions.delete",
    "logging.exclusions.get",
    "logging.exclusions.list",
    "logging.exclusions.update",
    "logging.fields.access",
    "logging.links.create",
    "logging.links.delete",
    "logging.links.get",
    "logging.links.list",
    "logging.locations.get",
    "logging.locations.list",
    "logging.logEntries.create",
    "logging.logEntries.download",
    "logging.logEntries.list",
    "logging.logMetrics.create",
    "logging.logMetrics.delete",
    "logging.logMetrics.get",
    "logging.logMetrics.list",
    "logging.logMetrics.update",
    "logging.logServiceIndexes.list",
    "logging.logServices.list",
    "logging.logs.delete",
    "logging.logs.list",
    "logging.notificationRules.create",
    "logging.notificationRules.delete",
    "logging.notificationRules.get",
    "logging.notificationRules.list",
    "logging.notificationRules.update",
    "logging.operations.cancel",
    "logging.operations.get",
    "logging.operations.list",
    "logging.privateLogEntries.list",
    "logging.queries.create",
    "logging.queries.delete",
    "logging.queries.get",
    "logging.queries.list",
    "logging.queries.listShared",
    "logging.queries.share",
    "logging.queries.update",
    "logging.queries.updateShared",
    "logging.settings.get",
    "logging.settings.update",
    "logging.sinks.create",
    "logging.sinks.delete",
    "logging.sinks.get",
    "logging.sinks.list",
    "logging.sinks.update",
    "logging.usage.get",
    "logging.views.access",
    "logging.views.create",
    "logging.views.delete",
    "logging.views.get",
    "logging.views.list",
    "logging.views.listLogs",
    "logging.views.listResourceKeys",
    "logging.views.listResourceValues",
    "logging.views.update",
    "managedidentities.backups.create",
    "managedidentities.backups.delete",
    "managedidentities.backups.get",
    "managedidentities.backups.getIamPolicy",
    "managedidentities.backups.list",
    "managedidentities.backups.setIamPolicy",
    "managedidentities.backups.update",
    "managedidentities.domains.attachTrust",
    "managedidentities.domains.checkMigrationPermission",
    "managedidentities.domains.create",
    "managedidentities.domains.createTagBinding",
    "managedidentities.domains.delete",
    "managedidentities.domains.deleteTagBinding",
    "managedidentities.domains.detachTrust",
    "managedidentities.domains.disableMigration",
    "managedidentities.domains.domainJoinMachine",
    "managedidentities.domains.enableMigration",
    "managedidentities.domains.extendSchema",
    "managedidentities.domains.get",
    "managedidentities.domains.getIamPolicy",
    "managedidentities.domains.list",
    "managedidentities.domains.listEffectiveTags",
    "managedidentities.domains.listTagBindings",
    "managedidentities.domains.reconfigureTrust",
    "managedidentities.domains.resetpassword",
    "managedidentities.domains.restore",
    "managedidentities.domains.setIamPolicy",
    "managedidentities.domains.update",
    "managedidentities.domains.updateLDAPSSettings",
    "managedidentities.domains.validateTrust",
    "managedidentities.locations.get",
    "managedidentities.locations.list",
    "managedidentities.operations.cancel",
    "managedidentities.operations.delete",
    "managedidentities.operations.get",
    "managedidentities.operations.list",
    "managedidentities.peerings.create",
    "managedidentities.peerings.delete",
    "managedidentities.peerings.get",
    "managedidentities.peerings.getIamPolicy",
    "managedidentities.peerings.list",
    "managedidentities.peerings.setIamPolicy",
    "managedidentities.peerings.update",
    "managedidentities.sqlintegrations.get",
    "managedidentities.sqlintegrations.list",
    "mapsadmin.clientMaps.create",
    "mapsadmin.clientMaps.delete",
    "mapsadmin.clientMaps.get",
    "mapsadmin.clientMaps.list",
    "mapsadmin.clientMaps.update",
    "mapsadmin.clientStyleActivationRules.update",
    "mapsadmin.clientStyleSheetSnapshots.list",
    "mapsadmin.clientStyleSheetSnapshots.update",
    "mapsadmin.clientStyles.create",
    "mapsadmin.clientStyles.delete",
    "mapsadmin.clientStyles.get",
    "mapsadmin.clientStyles.list",
    "mapsadmin.clientStyles.update",
    "mapsadmin.styleEditorConfigs.get",
    "mapsadmin.styleSnapshots.list",
    "mapsadmin.styleSnapshots.update",
    "memcache.instances.applyParameters",
    "memcache.instances.applySoftwareUpdate",
    "memcache.instances.create",
    "memcache.instances.delete",
    "memcache.instances.get",
    "memcache.instances.list",
    "memcache.instances.rescheduleMaintenance",
    "memcache.instances.update",
    "memcache.instances.updateParameters",
    "memcache.locations.get",
    "memcache.locations.list",
    "memcache.operations.cancel",
    "memcache.operations.delete",
    "memcache.operations.get",
    "memcache.operations.list",
    "meshconfig.projects.get",
    "meshconfig.projects.init",
    "metastore.backups.create",
    "metastore.backups.delete",
    "metastore.backups.get",
    "metastore.backups.getIamPolicy",
    "metastore.backups.list",
    "metastore.backups.setIamPolicy",
    "metastore.backups.use",
    "metastore.databases.create",
    "metastore.databases.delete",
    "metastore.databases.get",
    "metastore.databases.getIamPolicy",
    "metastore.databases.list",
    "metastore.databases.setIamPolicy",
    "metastore.databases.update",
    "metastore.federations.create",
    "metastore.federations.delete",
    "metastore.federations.get",
    "metastore.federations.getIamPolicy",
    "metastore.federations.list",
    "metastore.federations.setIamPolicy",
    "metastore.federations.update",
    "metastore.federations.use",
    "metastore.imports.create",
    "metastore.imports.get",
    "metastore.imports.list",
    "metastore.imports.update",
    "metastore.locations.get",
    "metastore.locations.list",
    "metastore.operations.cancel",
    "metastore.operations.delete",
    "metastore.operations.get",
    "metastore.operations.list",
    "metastore.services.create",
    "metastore.services.delete",
    "metastore.services.export",
    "metastore.services.get",
    "metastore.services.getIamPolicy",
    "metastore.services.list",
    "metastore.services.mutateMetadata",
    "metastore.services.queryMetadata",
    "metastore.services.restore",
    "metastore.services.setIamPolicy",
    "metastore.services.update",
    "metastore.services.use",
    "metastore.tables.create",
    "metastore.tables.delete",
    "metastore.tables.get",
    "metastore.tables.getIamPolicy",
    "metastore.tables.list",
    "metastore.tables.setIamPolicy",
    "metastore.tables.update",
    "migrationcenter.assets.create",
    "migrationcenter.assets.delete",
    "migrationcenter.assets.get",
    "migrationcenter.assets.list",
    "migrationcenter.assets.reportFrames",
    "migrationcenter.assets.update",
    "migrationcenter.groups.create",
    "migrationcenter.groups.delete",
    "migrationcenter.groups.get",
    "migrationcenter.groups.list",
    "migrationcenter.groups.update",
    "migrationcenter.importJobs.create",
    "migrationcenter.importJobs.delete",
    "migrationcenter.importJobs.get",
    "migrationcenter.importJobs.list",
    "migrationcenter.importJobs.update",
    "migrationcenter.locations.get",
    "migrationcenter.locations.list",
    "migrationcenter.operations.cancel",
    "migrationcenter.operations.delete",
    "migrationcenter.operations.get",
    "migrationcenter.operations.list",
    "migrationcenter.sources.create",
    "migrationcenter.sources.delete",
    "migrationcenter.sources.get",
    "migrationcenter.sources.list",
    "migrationcenter.sources.update",
    "ml.jobs.cancel",
    "ml.jobs.create",
    "ml.jobs.get",
    "ml.jobs.getIamPolicy",
    "ml.jobs.list",
    "ml.jobs.setIamPolicy",
    "ml.jobs.update",
    "ml.locations.get",
    "ml.locations.list",
    "ml.models.create",
    "ml.models.delete",
    "ml.models.get",
    "ml.models.getIamPolicy",
    "ml.models.list",
    "ml.models.predict",
    "ml.models.setIamPolicy",
    "ml.models.update",
    "ml.operations.cancel",
    "ml.operations.get",
    "ml.operations.list",
    "ml.projects.getConfig",
    "ml.studies.create",
    "ml.studies.delete",
    "ml.studies.get",
    "ml.studies.getIamPolicy",
    "ml.studies.list",
    "ml.studies.setIamPolicy",
    "ml.trials.create",
    "ml.trials.delete",
    "ml.trials.get",
    "ml.trials.list",
    "ml.trials.update",
    "ml.versions.create",
    "ml.versions.delete",
    "ml.versions.get",
    "ml.versions.list",
    "ml.versions.predict",
    "ml.versions.update",
    "monitoring.alertPolicies.create",
    "monitoring.alertPolicies.delete",
    "monitoring.alertPolicies.get",
    "monitoring.alertPolicies.list",
    "monitoring.alertPolicies.update",
    "monitoring.dashboards.create",
    "monitoring.dashboards.delete",
    "monitoring.dashboards.get",
    "monitoring.dashboards.list",
    "monitoring.dashboards.update",
    "monitoring.groups.create",
    "monitoring.groups.delete",
    "monitoring.groups.get",
    "monitoring.groups.list",
    "monitoring.groups.update",
    "monitoring.metricDescriptors.create",
    "monitoring.metricDescriptors.delete",
    "monitoring.metricDescriptors.get",
    "monitoring.metricDescriptors.list",
    "monitoring.metricsScopes.link",
    "monitoring.monitoredResourceDescriptors.get",
    "monitoring.monitoredResourceDescriptors.list",
    "monitoring.notificationChannelDescriptors.get",
    "monitoring.notificationChannelDescriptors.list",
    "monitoring.notificationChannels.create",
    "monitoring.notificationChannels.delete",
    "monitoring.notificationChannels.get",
    "monitoring.notificationChannels.getVerificationCode",
    "monitoring.notificationChannels.list",
    "monitoring.notificationChannels.sendVerificationCode",
    "monitoring.notificationChannels.update",
    "monitoring.notificationChannels.verify",
    "monitoring.publicWidgets.create",
    "monitoring.publicWidgets.delete",
    "monitoring.publicWidgets.get",
    "monitoring.publicWidgets.list",
    "monitoring.publicWidgets.update",
    "monitoring.services.create",
    "monitoring.services.delete",
    "monitoring.services.get",
    "monitoring.services.list",
    "monitoring.services.update",
    "monitoring.slos.create",
    "monitoring.slos.delete",
    "monitoring.slos.get",
    "monitoring.slos.list",
    "monitoring.slos.update",
    "monitoring.timeSeries.create",
    "monitoring.timeSeries.list",
    "monitoring.uptimeCheckConfigs.create",
    "monitoring.uptimeCheckConfigs.delete",
    "monitoring.uptimeCheckConfigs.get",
    "monitoring.uptimeCheckConfigs.list",
    "monitoring.uptimeCheckConfigs.update",
    "nestconsole.smarthomePreviews.update",
    "nestconsole.smarthomeProjects.create",
    "nestconsole.smarthomeProjects.delete",
    "nestconsole.smarthomeProjects.get",
    "nestconsole.smarthomeProjects.update",
    "nestconsole.smarthomeVersions.create",
    "nestconsole.smarthomeVersions.get",
    "nestconsole.smarthomeVersions.submit",
    "networkconnectivity.hubs.create",
    "networkconnectivity.hubs.delete",
    "networkconnectivity.hubs.get",
    "networkconnectivity.hubs.getIamPolicy",
    "networkconnectivity.hubs.list",
    "networkconnectivity.hubs.setIamPolicy",
    "networkconnectivity.hubs.update",
    "networkconnectivity.internalRanges.create",
    "networkconnectivity.internalRanges.delete",
    "networkconnectivity.internalRanges.get",
    "networkconnectivity.internalRanges.getIamPolicy",
    "networkconnectivity.internalRanges.list",
    "networkconnectivity.internalRanges.setIamPolicy",
    "networkconnectivity.internalRanges.update",
    "networkconnectivity.locations.get",
    "networkconnectivity.locations.list",
    "networkconnectivity.operations.cancel",
    "networkconnectivity.operations.delete",
    "networkconnectivity.operations.get",
    "networkconnectivity.operations.list",
    "networkconnectivity.policyBasedRoutes.create",
    "networkconnectivity.policyBasedRoutes.delete",
    "networkconnectivity.policyBasedRoutes.get",
    "networkconnectivity.policyBasedRoutes.getIamPolicy",
    "networkconnectivity.policyBasedRoutes.list",
    "networkconnectivity.policyBasedRoutes.setIamPolicy",
    "networkconnectivity.spokes.create",
    "networkconnectivity.spokes.delete",
    "networkconnectivity.spokes.get",
    "networkconnectivity.spokes.getIamPolicy",
    "networkconnectivity.spokes.list",
    "networkconnectivity.spokes.setIamPolicy",
    "networkconnectivity.spokes.update",
    "networkmanagement.config.get",
    "networkmanagement.config.startFreeTrial",
    "networkmanagement.config.update",
    "networkmanagement.connectivitytests.create",
    "networkmanagement.connectivitytests.delete",
    "networkmanagement.connectivitytests.get",
    "networkmanagement.connectivitytests.getIamPolicy",
    "networkmanagement.connectivitytests.list",
    "networkmanagement.connectivitytests.rerun",
    "networkmanagement.connectivitytests.setIamPolicy",
    "networkmanagement.connectivitytests.update",
    "networkmanagement.locations.get",
    "networkmanagement.locations.list",
    "networkmanagement.operations.get",
    "networkmanagement.operations.list",
    "networksecurity.authorizationPolicies.create",
    "networksecurity.authorizationPolicies.delete",
    "networksecurity.authorizationPolicies.get",
    "networksecurity.authorizationPolicies.getIamPolicy",
    "networksecurity.authorizationPolicies.list",
    "networksecurity.authorizationPolicies.setIamPolicy",
    "networksecurity.authorizationPolicies.update",
    "networksecurity.authorizationPolicies.use",
    "networksecurity.clientTlsPolicies.create",
    "networksecurity.clientTlsPolicies.delete",
    "networksecurity.clientTlsPolicies.get",
    "networksecurity.clientTlsPolicies.getIamPolicy",
    "networksecurity.clientTlsPolicies.list",
    "networksecurity.clientTlsPolicies.setIamPolicy",
    "networksecurity.clientTlsPolicies.update",
    "networksecurity.clientTlsPolicies.use",
    "networksecurity.locations.get",
    "networksecurity.locations.list",
    "networksecurity.operations.cancel",
    "networksecurity.operations.delete",
    "networksecurity.operations.get",
    "networksecurity.operations.list",
    "networksecurity.serverTlsPolicies.create",
    "networksecurity.serverTlsPolicies.delete",
    "networksecurity.serverTlsPolicies.get",
    "networksecurity.serverTlsPolicies.getIamPolicy",
    "networksecurity.serverTlsPolicies.list",
    "networksecurity.serverTlsPolicies.setIamPolicy",
    "networksecurity.serverTlsPolicies.update",
    "networksecurity.serverTlsPolicies.use",
    "networkservices.endpointConfigSelectors.create",
    "networkservices.endpointConfigSelectors.delete",
    "networkservices.endpointConfigSelectors.get",
    "networkservices.endpointConfigSelectors.getIamPolicy",
    "networkservices.endpointConfigSelectors.list",
    "networkservices.endpointConfigSelectors.setIamPolicy",
    "networkservices.endpointConfigSelectors.update",
    "networkservices.endpointConfigSelectors.use",
    "networkservices.endpointPolicies.create",
    "networkservices.endpointPolicies.delete",
    "networkservices.endpointPolicies.get",
    "networkservices.endpointPolicies.getIamPolicy",
    "networkservices.endpointPolicies.list",
    "networkservices.endpointPolicies.setIamPolicy",
    "networkservices.endpointPolicies.update",
    "networkservices.endpointPolicies.use",
    "networkservices.gateways.create",
    "networkservices.gateways.delete",
    "networkservices.gateways.get",
    "networkservices.gateways.list",
    "networkservices.gateways.update",
    "networkservices.gateways.use",
    "networkservices.grpcRoutes.create",
    "networkservices.grpcRoutes.delete",
    "networkservices.grpcRoutes.get",
    "networkservices.grpcRoutes.getIamPolicy",
    "networkservices.grpcRoutes.list",
    "networkservices.grpcRoutes.setIamPolicy",
    "networkservices.grpcRoutes.update",
    "networkservices.grpcRoutes.use",
    "networkservices.httpFilters.create",
    "networkservices.httpFilters.delete",
    "networkservices.httpFilters.get",
    "networkservices.httpFilters.getIamPolicy",
    "networkservices.httpFilters.list",
    "networkservices.httpFilters.setIamPolicy",
    "networkservices.httpFilters.update",
    "networkservices.httpFilters.use",
    "networkservices.httpRoutes.create",
    "networkservices.httpRoutes.delete",
    "networkservices.httpRoutes.get",
    "networkservices.httpRoutes.getIamPolicy",
    "networkservices.httpRoutes.list",
    "networkservices.httpRoutes.setIamPolicy",
    "networkservices.httpRoutes.update",
    "networkservices.httpRoutes.use",
    "networkservices.httpfilters.create",
    "networkservices.httpfilters.delete",
    "networkservices.httpfilters.get",
    "networkservices.httpfilters.getIamPolicy",
    "networkservices.httpfilters.list",
    "networkservices.httpfilters.setIamPolicy",
    "networkservices.httpfilters.update",
    "networkservices.httpfilters.use",
    "networkservices.locations.get",
    "networkservices.locations.list",
    "networkservices.meshes.create",
    "networkservices.meshes.delete",
    "networkservices.meshes.get",
    "networkservices.meshes.getIamPolicy",
    "networkservices.meshes.list",
    "networkservices.meshes.setIamPolicy",
    "networkservices.meshes.update",
    "networkservices.meshes.use",
    "networkservices.operations.cancel",
    "networkservices.operations.delete",
    "networkservices.operations.get",
    "networkservices.operations.list",
    "networkservices.serviceBindings.create",
    "networkservices.serviceBindings.delete",
    "networkservices.serviceBindings.get",
    "networkservices.serviceBindings.list",
    "networkservices.serviceBindings.update",
    "networkservices.tcpRoutes.create",
    "networkservices.tcpRoutes.delete",
    "networkservices.tcpRoutes.get",
    "networkservices.tcpRoutes.getIamPolicy",
    "networkservices.tcpRoutes.list",
    "networkservices.tcpRoutes.setIamPolicy",
    "networkservices.tcpRoutes.update",
    "networkservices.tcpRoutes.use",
    "networkservices.tlsRoutes.create",
    "networkservices.tlsRoutes.delete",
    "networkservices.tlsRoutes.get",
    "networkservices.tlsRoutes.list",
    "networkservices.tlsRoutes.update",
    "networkservices.tlsRoutes.use",
    "notebooks.environments.create",
    "notebooks.environments.delete",
    "notebooks.environments.get",
    "notebooks.environments.getIamPolicy",
    "notebooks.environments.list",
    "notebooks.environments.setIamPolicy",
    "notebooks.executions.create",
    "notebooks.executions.delete",
    "notebooks.executions.get",
    "notebooks.executions.getIamPolicy",
    "notebooks.executions.list",
    "notebooks.executions.setIamPolicy",
    "notebooks.instances.checkUpgradability",
    "notebooks.instances.create",
    "notebooks.instances.delete",
    "notebooks.instances.diagnose",
    "notebooks.instances.get",
    "notebooks.instances.getHealth",
    "notebooks.instances.getIamPolicy",
    "notebooks.instances.list",
    "notebooks.instances.reset",
    "notebooks.instances.setAccelerator",
    "notebooks.instances.setIamPolicy",
    "notebooks.instances.setLabels",
    "notebooks.instances.setMachineType",
    "notebooks.instances.start",
    "notebooks.instances.stop",
    "notebooks.instances.update",
    "notebooks.instances.updateConfig",
    "notebooks.instances.updateShieldInstanceConfig",
    "notebooks.instances.upgrade",
    "notebooks.instances.use",
    "notebooks.locations.get",
    "notebooks.locations.list",
    "notebooks.operations.cancel",
    "notebooks.operations.delete",
    "notebooks.operations.get",
    "notebooks.operations.list",
    "notebooks.runtimes.create",
    "notebooks.runtimes.delete",
    "notebooks.runtimes.diagnose",
    "notebooks.runtimes.get",
    "notebooks.runtimes.getIamPolicy",
    "notebooks.runtimes.list",
    "notebooks.runtimes.reset",
    "notebooks.runtimes.setIamPolicy",
    "notebooks.runtimes.start",
    "notebooks.runtimes.stop",
    "notebooks.runtimes.switch",
    "notebooks.runtimes.update",
    "notebooks.schedules.create",
    "notebooks.schedules.delete",
    "notebooks.schedules.get",
    "notebooks.schedules.getIamPolicy",
    "notebooks.schedules.list",
    "notebooks.schedules.setIamPolicy",
    "oauthconfig.clientpolicy.get",
    "oauthconfig.testusers.get",
    "oauthconfig.testusers.update",
    "oauthconfig.verification.get",
    "oauthconfig.verification.submit",
    "oauthconfig.verification.update",
    "ondemandscanning.operations.cancel",
    "ondemandscanning.operations.delete",
    "ondemandscanning.operations.get",
    "ondemandscanning.operations.list",
    "ondemandscanning.operations.wait",
    "ondemandscanning.scans.analyzePackages",
    "ondemandscanning.scans.listVulnerabilities",
    "ondemandscanning.scans.scan",
    "opsconfigmonitoring.resourceMetadata.list",
    "opsconfigmonitoring.resourceMetadata.write",
    "orgpolicy.constraints.list",
    "orgpolicy.policies.create",
    "orgpolicy.policies.delete",
    "orgpolicy.policies.list",
    "orgpolicy.policies.update",
    "orgpolicy.policy.get",
    "orgpolicy.policy.set",
    "osconfig.guestPolicies.create",
    "osconfig.guestPolicies.delete",
    "osconfig.guestPolicies.get",
    "osconfig.guestPolicies.list",
    "osconfig.guestPolicies.update",
    "osconfig.instanceOSPoliciesCompliances.get",
    "osconfig.instanceOSPoliciesCompliances.list",
    "osconfig.inventories.get",
    "osconfig.inventories.list",
    "osconfig.osPolicyAssignmentReports.get",
    "osconfig.osPolicyAssignmentReports.list",
    "osconfig.osPolicyAssignments.create",
    "osconfig.osPolicyAssignments.delete",
    "osconfig.osPolicyAssignments.get",
    "osconfig.osPolicyAssignments.list",
    "osconfig.osPolicyAssignments.update",
    "osconfig.patchDeployments.create",
    "osconfig.patchDeployments.delete",
    "osconfig.patchDeployments.execute",
    "osconfig.patchDeployments.get",
    "osconfig.patchDeployments.list",
    "osconfig.patchDeployments.pause",
    "osconfig.patchDeployments.resume",
    "osconfig.patchDeployments.update",
    "osconfig.patchJobs.exec",
    "osconfig.patchJobs.get",
    "osconfig.patchJobs.list",
    "osconfig.vulnerabilityReports.get",
    "osconfig.vulnerabilityReports.list",
    "paymentsresellersubscription.products.list",
    "paymentsresellersubscription.promotions.list",
    "paymentsresellersubscription.subscriptions.cancel",
    "paymentsresellersubscription.subscriptions.extend",
    "paymentsresellersubscription.subscriptions.get",
    "paymentsresellersubscription.subscriptions.provision",
    "paymentsresellersubscription.subscriptions.undoCancel",
    "policyanalyzer.serviceAccountKeyLastAuthenticationActivities.query",
    "policyanalyzer.serviceAccountLastAuthenticationActivities.query",
    "policysimulator.replayResults.list",
    "policysimulator.replays.create",
    "policysimulator.replays.get",
    "policysimulator.replays.list",
    "policysimulator.replays.run",
    "privateca.caPools.create",
    "privateca.caPools.delete",
    "privateca.caPools.get",
    "privateca.caPools.getIamPolicy",
    "privateca.caPools.list",
    "privateca.caPools.setIamPolicy",
    "privateca.caPools.update",
    "privateca.caPools.use",
    "privateca.certificateAuthorities.create",
    "privateca.certificateAuthorities.delete",
    "privateca.certificateAuthorities.get",
    "privateca.certificateAuthorities.getIamPolicy",
    "privateca.certificateAuthorities.list",
    "privateca.certificateAuthorities.setIamPolicy",
    "privateca.certificateAuthorities.update",
    "privateca.certificateRevocationLists.create",
    "privateca.certificateRevocationLists.get",
    "privateca.certificateRevocationLists.getIamPolicy",
    "privateca.certificateRevocationLists.list",
    "privateca.certificateRevocationLists.setIamPolicy",
    "privateca.certificateRevocationLists.update",
    "privateca.certificateTemplates.create",
    "privateca.certificateTemplates.delete",
    "privateca.certificateTemplates.get",
    "privateca.certificateTemplates.getIamPolicy",
    "privateca.certificateTemplates.list",
    "privateca.certificateTemplates.setIamPolicy",
    "privateca.certificateTemplates.update",
    "privateca.certificateTemplates.use",
    "privateca.certificates.create",
    "privateca.certificates.createForSelf",
    "privateca.certificates.get",
    "privateca.certificates.getIamPolicy",
    "privateca.certificates.list",
    "privateca.certificates.setIamPolicy",
    "privateca.certificates.update",
    "privateca.locations.get",
    "privateca.locations.list",
    "privateca.operations.cancel",
    "privateca.operations.delete",
    "privateca.operations.get",
    "privateca.operations.list",
    "privateca.reusableConfigs.create",
    "privateca.reusableConfigs.delete",
    "privateca.reusableConfigs.get",
    "privateca.reusableConfigs.getIamPolicy",
    "privateca.reusableConfigs.list",
    "privateca.reusableConfigs.setIamPolicy",
    "privateca.reusableConfigs.update",
    "proximitybeacon.attachments.create",
    "proximitybeacon.attachments.delete",
    "proximitybeacon.attachments.get",
    "proximitybeacon.attachments.list",
    "proximitybeacon.beacons.attach",
    "proximitybeacon.beacons.create",
    "proximitybeacon.beacons.get",
    "proximitybeacon.beacons.getIamPolicy",
    "proximitybeacon.beacons.list",
    "proximitybeacon.beacons.setIamPolicy",
    "proximitybeacon.beacons.update",
    "proximitybeacon.namespaces.create",
    "proximitybeacon.namespaces.delete",
    "proximitybeacon.namespaces.get",
    "proximitybeacon.namespaces.getIamPolicy",
    "proximitybeacon.namespaces.list",
    "proximitybeacon.namespaces.setIamPolicy",
    "proximitybeacon.namespaces.update",
    "publicca.externalAccountKeys.create",
    "pubsub.schemas.attach",
    "pubsub.schemas.create",
    "pubsub.schemas.delete",
    "pubsub.schemas.get",
    "pubsub.schemas.getIamPolicy",
    "pubsub.schemas.list",
    "pubsub.schemas.setIamPolicy",
    "pubsub.schemas.validate",
    "pubsub.snapshots.create",
    "pubsub.snapshots.delete",
    "pubsub.snapshots.get",
    "pubsub.snapshots.getIamPolicy",
    "pubsub.snapshots.list",
    "pubsub.snapshots.seek",
    "pubsub.snapshots.setIamPolicy",
    "pubsub.snapshots.update",
    "pubsub.subscriptions.consume",
    "pubsub.subscriptions.create",
    "pubsub.subscriptions.delete",
    "pubsub.subscriptions.get",
    "pubsub.subscriptions.getIamPolicy",
    "pubsub.subscriptions.list",
    "pubsub.subscriptions.setIamPolicy",
    "pubsub.subscriptions.update",
    "pubsub.topics.attachSubscription",
    "pubsub.topics.create",
    "pubsub.topics.delete",
    "pubsub.topics.detachSubscription",
    "pubsub.topics.get",
    "pubsub.topics.getIamPolicy",
    "pubsub.topics.list",
    "pubsub.topics.publish",
    "pubsub.topics.setIamPolicy",
    "pubsub.topics.update",
    "pubsub.topics.updateTag",
    "pubsublite.operations.get",
    "pubsublite.operations.list",
    "pubsublite.reservations.attachTopic",
    "pubsublite.reservations.create",
    "pubsublite.reservations.delete",
    "pubsublite.reservations.get",
    "pubsublite.reservations.list",
    "pubsublite.reservations.listTopics",
    "pubsublite.reservations.update",
    "pubsublite.subscriptions.create",
    "pubsublite.subscriptions.delete",
    "pubsublite.subscriptions.get",
    "pubsublite.subscriptions.getCursor",
    "pubsublite.subscriptions.list",
    "pubsublite.subscriptions.seek",
    "pubsublite.subscriptions.setCursor",
    "pubsublite.subscriptions.subscribe",
    "pubsublite.subscriptions.update",
    "pubsublite.topics.computeHeadCursor",
    "pubsublite.topics.computeMessageStats",
    "pubsublite.topics.computeTimeCursor",
    "pubsublite.topics.create",
    "pubsublite.topics.delete",
    "pubsublite.topics.get",
    "pubsublite.topics.getPartitions",
    "pubsublite.topics.list",
    "pubsublite.topics.listSubscriptions",
    "pubsublite.topics.publish",
    "pubsublite.topics.subscribe",
    "pubsublite.topics.update",
    "recaptchaenterprise.assessments.annotate",
    "recaptchaenterprise.assessments.create",
    "recaptchaenterprise.keys.create",
    "recaptchaenterprise.keys.delete",
    "recaptchaenterprise.keys.get",
    "recaptchaenterprise.keys.list",
    "recaptchaenterprise.keys.retrievelegacysecretkey",
    "recaptchaenterprise.keys.update",
    "recaptchaenterprise.metrics.get",
    "recaptchaenterprise.projectmetadata.get",
    "recaptchaenterprise.projectmetadata.update",
    "recaptchaenterprise.relatedaccountgroupmemberships.list",
    "recaptchaenterprise.relatedaccountgroups.list",
    "recommender.bigqueryCapacityCommitmentsInsights.get",
    "recommender.bigqueryCapacityCommitmentsInsights.list",
    "recommender.bigqueryCapacityCommitmentsInsights.update",
    "recommender.bigqueryCapacityCommitmentsRecommendations.get",
    "recommender.bigqueryCapacityCommitmentsRecommendations.list",
    "recommender.bigqueryCapacityCommitmentsRecommendations.update",
    "recommender.cloudAssetInsights.get",
    "recommender.cloudAssetInsights.list",
    "recommender.cloudAssetInsights.update",
    "recommender.cloudsqlIdleInstanceRecommendations.get",
    "recommender.cloudsqlIdleInstanceRecommendations.list",
    "recommender.cloudsqlIdleInstanceRecommendations.update",
    "recommender.cloudsqlInstanceActivityInsights.get",
    "recommender.cloudsqlInstanceActivityInsights.list",
    "recommender.cloudsqlInstanceActivityInsights.update",
    "recommender.cloudsqlInstanceCpuUsageInsights.get",
    "recommender.cloudsqlInstanceCpuUsageInsights.list",
    "recommender.cloudsqlInstanceCpuUsageInsights.update",
    "recommender.cloudsqlInstanceDiskUsageTrendInsights.get",
    "recommender.cloudsqlInstanceDiskUsageTrendInsights.list",
    "recommender.cloudsqlInstanceDiskUsageTrendInsights.update",
    "recommender.cloudsqlInstanceMemoryUsageInsights.get",
    "recommender.cloudsqlInstanceMemoryUsageInsights.list",
    "recommender.cloudsqlInstanceMemoryUsageInsights.update",
    "recommender.cloudsqlInstanceOutOfDiskRecommendations.get",
    "recommender.cloudsqlInstanceOutOfDiskRecommendations.list",
    "recommender.cloudsqlInstanceOutOfDiskRecommendations.update",
    "recommender.cloudsqlInstancePerformanceInsights.get",
    "recommender.cloudsqlInstancePerformanceInsights.list",
    "recommender.cloudsqlInstancePerformanceInsights.update",
    "recommender.cloudsqlInstancePerformanceRecommendations.get",
    "recommender.cloudsqlInstancePerformanceRecommendations.list",
    "recommender.cloudsqlInstancePerformanceRecommendations.update",
    "recommender.cloudsqlInstanceSecurityInsights.get",
    "recommender.cloudsqlInstanceSecurityInsights.list",
    "recommender.cloudsqlInstanceSecurityInsights.update",
    "recommender.cloudsqlInstanceSecurityRecommendations.get",
    "recommender.cloudsqlInstanceSecurityRecommendations.list",
    "recommender.cloudsqlInstanceSecurityRecommendations.update",
    "recommender.cloudsqlOverprovisionedInstanceRecommendations.get",
    "recommender.cloudsqlOverprovisionedInstanceRecommendations.list",
    "recommender.cloudsqlOverprovisionedInstanceRecommendations.update",
    "recommender.commitmentUtilizationInsights.get",
    "recommender.commitmentUtilizationInsights.list",
    "recommender.commitmentUtilizationInsights.update",
    "recommender.computeAddressIdleResourceInsights.get",
    "recommender.computeAddressIdleResourceInsights.list",
    "recommender.computeAddressIdleResourceInsights.update",
    "recommender.computeAddressIdleResourceRecommendations.get",
    "recommender.computeAddressIdleResourceRecommendations.list",
    "recommender.computeAddressIdleResourceRecommendations.update",
    "recommender.computeDiskIdleResourceInsights.get",
    "recommender.computeDiskIdleResourceInsights.list",
    "recommender.computeDiskIdleResourceInsights.update",
    "recommender.computeDiskIdleResourceRecommendations.get",
    "recommender.computeDiskIdleResourceRecommendations.list",
    "recommender.computeDiskIdleResourceRecommendations.update",
    "recommender.computeFirewallInsightTypeConfigs.get",
    "recommender.computeFirewallInsightTypeConfigs.update",
    "recommender.computeFirewallInsights.get",
    "recommender.computeFirewallInsights.list",
    "recommender.computeFirewallInsights.update",
    "recommender.computeImageIdleResourceInsights.get",
    "recommender.computeImageIdleResourceInsights.list",
    "recommender.computeImageIdleResourceInsights.update",
    "recommender.computeImageIdleResourceRecommendations.get",
    "recommender.computeImageIdleResourceRecommendations.list",
    "recommender.computeImageIdleResourceRecommendations.update",
    "recommender.computeInstanceCpuUsageInsights.get",
    "recommender.computeInstanceCpuUsageInsights.list",
    "recommender.computeInstanceCpuUsageInsights.update",
    "recommender.computeInstanceCpuUsagePredictionInsights.get",
    "recommender.computeInstanceCpuUsagePredictionInsights.list",
    "recommender.computeInstanceCpuUsagePredictionInsights.update",
    "recommender.computeInstanceCpuUsageTrendInsights.get",
    "recommender.computeInstanceCpuUsageTrendInsights.list",
    "recommender.computeInstanceCpuUsageTrendInsights.update",
    "recommender.computeInstanceGroupManagerCpuUsageInsights.get",
    "recommender.computeInstanceGroupManagerCpuUsageInsights.list",
    "recommender.computeInstanceGroupManagerCpuUsageInsights.update",
    "recommender.computeInstanceGroupManagerCpuUsagePredictionInsights.get",
    "recommender.computeInstanceGroupManagerCpuUsagePredictionInsights.list",
    "recommender.computeInstanceGroupManagerCpuUsagePredictionInsights.update",
    "recommender.computeInstanceGroupManagerCpuUsageTrendInsights.get",
    "recommender.computeInstanceGroupManagerCpuUsageTrendInsights.list",
    "recommender.computeInstanceGroupManagerCpuUsageTrendInsights.update",
    "recommender.computeInstanceGroupManagerMachineTypeRecommendations.get",
    "recommender.computeInstanceGroupManagerMachineTypeRecommendations.list",
    "recommender.computeInstanceGroupManagerMachineTypeRecommendations.update",
    "recommender.computeInstanceGroupManagerMemoryUsageInsights.get",
    "recommender.computeInstanceGroupManagerMemoryUsageInsights.list",
    "recommender.computeInstanceGroupManagerMemoryUsageInsights.update",
    "recommender.computeInstanceGroupManagerMemoryUsagePredictionInsights.get",
    "recommender.computeInstanceGroupManagerMemoryUsagePredictionInsights.list",
    "recommender.computeInstanceGroupManagerMemoryUsagePredictionInsights.update",
    "recommender.computeInstanceIdleResourceRecommendations.get",
    "recommender.computeInstanceIdleResourceRecommendations.list",
    "recommender.computeInstanceIdleResourceRecommendations.update",
    "recommender.computeInstanceMachineTypeRecommendations.get",
    "recommender.computeInstanceMachineTypeRecommendations.list",
    "recommender.computeInstanceMachineTypeRecommendations.update",
    "recommender.computeInstanceMemoryUsageInsights.get",
    "recommender.computeInstanceMemoryUsageInsights.list",
    "recommender.computeInstanceMemoryUsageInsights.update",
    "recommender.computeInstanceMemoryUsagePredictionInsights.get",
    "recommender.computeInstanceMemoryUsagePredictionInsights.list",
    "recommender.computeInstanceMemoryUsagePredictionInsights.update",
    "recommender.computeInstanceNetworkThroughputInsights.get",
    "recommender.computeInstanceNetworkThroughputInsights.list",
    "recommender.computeInstanceNetworkThroughputInsights.update",
    "recommender.containerDiagnosisInsights.get",
    "recommender.containerDiagnosisInsights.list",
    "recommender.containerDiagnosisInsights.update",
    "recommender.containerDiagnosisRecommendations.get",
    "recommender.containerDiagnosisRecommendations.list",
    "recommender.containerDiagnosisRecommendations.update",
    "recommender.costInsights.get",
    "recommender.costInsights.list",
    "recommender.costInsights.update",
    "recommender.dataflowDiagnosticsInsights.get",
    "recommender.dataflowDiagnosticsInsights.list",
    "recommender.dataflowDiagnosticsInsights.update",
    "recommender.errorReportingInsights.get",
    "recommender.errorReportingInsights.list",
    "recommender.errorReportingInsights.update",
    "recommender.errorReportingRecommendations.get",
    "recommender.errorReportingRecommendations.list",
    "recommender.errorReportingRecommendations.update",
    "recommender.gmpGuidedExperienceInsights.get",
    "recommender.gmpGuidedExperienceInsights.list",
    "recommender.gmpGuidedExperienceInsights.update",
    "recommender.gmpGuidedExperienceRecommendations.get",
    "recommender.gmpGuidedExperienceRecommendations.list",
    "recommender.gmpGuidedExperienceRecommendations.update",
    "recommender.gmpProjectManagementInsights.get",
    "recommender.gmpProjectManagementInsights.list",
    "recommender.gmpProjectManagementInsights.update",
    "recommender.gmpProjectManagementRecommendations.get",
    "recommender.gmpProjectManagementRecommendations.list",
    "recommender.gmpProjectManagementRecommendations.update",
    "recommender.gmpProjectProductSuggestionsInsights.get",
    "recommender.gmpProjectProductSuggestionsInsights.list",
    "recommender.gmpProjectProductSuggestionsInsights.update",
    "recommender.gmpProjectProductSuggestionsRecommendations.get",
    "recommender.gmpProjectProductSuggestionsRecommendations.list",
    "recommender.gmpProjectProductSuggestionsRecommendations.update",
    "recommender.gmpProjectQuotaInsights.get",
    "recommender.gmpProjectQuotaInsights.list",
    "recommender.gmpProjectQuotaInsights.update",
    "recommender.gmpProjectQuotaRecommendations.get",
    "recommender.gmpProjectQuotaRecommendations.list",
    "recommender.gmpProjectQuotaRecommendations.update",
    "recommender.iamPolicyInsights.get",
    "recommender.iamPolicyInsights.list",
    "recommender.iamPolicyInsights.update",
    "recommender.iamPolicyLateralMovementInsights.get",
    "recommender.iamPolicyLateralMovementInsights.list",
    "recommender.iamPolicyLateralMovementInsights.update",
    "recommender.iamPolicyRecommendations.get",
    "recommender.iamPolicyRecommendations.list",
    "recommender.iamPolicyRecommendations.update",
    "recommender.iamServiceAccountInsights.get",
    "recommender.iamServiceAccountInsights.list",
    "recommender.iamServiceAccountInsights.update",
    "recommender.locations.get",
    "recommender.locations.list",
    "recommender.loggingProductSuggestionContainerInsights.get",
    "recommender.loggingProductSuggestionContainerInsights.list",
    "recommender.loggingProductSuggestionContainerInsights.update",
    "recommender.loggingProductSuggestionContainerRecommendations.get",
    "recommender.loggingProductSuggestionContainerRecommendations.list",
    "recommender.loggingProductSuggestionContainerRecommendations.update",
    "recommender.monitoringProductSuggestionComputeInsights.get",
    "recommender.monitoringProductSuggestionComputeInsights.list",
    "recommender.monitoringProductSuggestionComputeInsights.update",
    "recommender.monitoringProductSuggestionComputeRecommendations.get",
    "recommender.monitoringProductSuggestionComputeRecommendations.list",
    "recommender.monitoringProductSuggestionComputeRecommendations.update",
    "recommender.networkAnalyzerCloudSqlInsights.get",
    "recommender.networkAnalyzerCloudSqlInsights.list",
    "recommender.networkAnalyzerCloudSqlInsights.update",
    "recommender.networkAnalyzerDynamicRouteInsights.get",
    "recommender.networkAnalyzerDynamicRouteInsights.list",
    "recommender.networkAnalyzerDynamicRouteInsights.update",
    "recommender.networkAnalyzerGkeConnectivityInsights.get",
    "recommender.networkAnalyzerGkeConnectivityInsights.list",
    "recommender.networkAnalyzerGkeConnectivityInsights.update",
    "recommender.networkAnalyzerGkeIpAddressInsights.get",
    "recommender.networkAnalyzerGkeIpAddressInsights.list",
    "recommender.networkAnalyzerGkeIpAddressInsights.update",
    "recommender.networkAnalyzerIpAddressInsights.get",
    "recommender.networkAnalyzerIpAddressInsights.list",
    "recommender.networkAnalyzerIpAddressInsights.update",
    "recommender.networkAnalyzerLoadBalancerInsights.get",
    "recommender.networkAnalyzerLoadBalancerInsights.list",
    "recommender.networkAnalyzerLoadBalancerInsights.update",
    "recommender.networkAnalyzerVpcConnectivityInsights.get",
    "recommender.networkAnalyzerVpcConnectivityInsights.list",
    "recommender.networkAnalyzerVpcConnectivityInsights.update",
    "recommender.resourcemanagerProjectUtilizationInsightTypeConfigs.get",
    "recommender.resourcemanagerProjectUtilizationInsightTypeConfigs.update",
    "recommender.resourcemanagerProjectUtilizationInsights.get",
    "recommender.resourcemanagerProjectUtilizationInsights.list",
    "recommender.resourcemanagerProjectUtilizationInsights.update",
    "recommender.resourcemanagerProjectUtilizationRecommendations.get",
    "recommender.resourcemanagerProjectUtilizationRecommendations.list",
    "recommender.resourcemanagerProjectUtilizationRecommendations.update",
    "recommender.resourcemanagerProjectUtilizationRecommenderConfigs.get",
    "recommender.resourcemanagerProjectUtilizationRecommenderConfigs.update",
    "recommender.runServiceIdentityInsights.get",
    "recommender.runServiceIdentityInsights.list",
    "recommender.runServiceIdentityInsights.update",
    "recommender.runServiceIdentityRecommendations.get",
    "recommender.runServiceIdentityRecommendations.list",
    "recommender.runServiceIdentityRecommendations.update",
    "recommender.runServiceSecurityInsights.get",
    "recommender.runServiceSecurityInsights.list",
    "recommender.runServiceSecurityInsights.update",
    "recommender.runServiceSecurityRecommendations.get",
    "recommender.runServiceSecurityRecommendations.list",
    "recommender.runServiceSecurityRecommendations.update",
    "recommender.spendBasedCommitmentInsights.get",
    "recommender.spendBasedCommitmentInsights.list",
    "recommender.spendBasedCommitmentInsights.update",
    "recommender.spendBasedCommitmentRecommendations.get",
    "recommender.spendBasedCommitmentRecommendations.list",
    "recommender.spendBasedCommitmentRecommendations.update",
    "recommender.usageCommitmentRecommendations.get",
    "recommender.usageCommitmentRecommendations.list",
    "recommender.usageCommitmentRecommendations.update",
    "redis.instances.create",
    "redis.instances.delete",
    "redis.instances.export",
    "redis.instances.failover",
    "redis.instances.get",
    "redis.instances.getAuthString",
    "redis.instances.import",
    "redis.instances.list",
    "redis.instances.rescheduleMaintenance",
    "redis.instances.update",
    "redis.instances.updateAuth",
    "redis.instances.upgrade",
    "redis.locations.get",
    "redis.locations.list",
    "redis.operations.cancel",
    "redis.operations.delete",
    "redis.operations.get",
    "redis.operations.list",
    "remotebuildexecution.actions.create",
    "remotebuildexecution.actions.delete",
    "remotebuildexecution.actions.get",
    "remotebuildexecution.actions.set",
    "remotebuildexecution.actions.update",
    "remotebuildexecution.blobs.create",
    "remotebuildexecution.blobs.get",
    "remotebuildexecution.botsessions.create",
    "remotebuildexecution.botsessions.update",
    "remotebuildexecution.instances.create",
    "remotebuildexecution.instances.delete",
    "remotebuildexecution.instances.get",
    "remotebuildexecution.instances.list",
    "remotebuildexecution.instances.update",
    "remotebuildexecution.logstreams.create",
    "remotebuildexecution.logstreams.get",
    "remotebuildexecution.logstreams.update",
    "remotebuildexecution.workerpools.create",
    "remotebuildexecution.workerpools.delete",
    "remotebuildexecution.workerpools.get",
    "remotebuildexecution.workerpools.list",
    "remotebuildexecution.workerpools.update",
    "resourcemanager.hierarchyNodes.createTagBinding",
    "resourcemanager.hierarchyNodes.deleteTagBinding",
    "resourcemanager.hierarchyNodes.listEffectiveTags",
    "resourcemanager.hierarchyNodes.listTagBindings",
    "resourcemanager.projects.createBillingAssignment",
    "resourcemanager.projects.delete",
    "resourcemanager.projects.deleteBillingAssignment",
    "resourcemanager.projects.get",
    "resourcemanager.projects.getIamPolicy",
    "resourcemanager.projects.move",
    "resourcemanager.projects.setIamPolicy",
    "resourcemanager.projects.undelete",
    "resourcemanager.projects.update",
    "resourcemanager.projects.updateLiens",
    "resourcesettings.settings.get",
    "resourcesettings.settings.list",
    "resourcesettings.settings.update",
    "retail.attributesConfigs.addCatalogAttribute",
    "retail.attributesConfigs.batchRemoveCatalogAttributes",
    "retail.attributesConfigs.exportCatalogAttributes",
    "retail.attributesConfigs.get",
    "retail.attributesConfigs.importCatalogAttributes",
    "retail.attributesConfigs.removeCatalogAttribute",
    "retail.attributesConfigs.replaceCatalogAttribute",
    "retail.attributesConfigs.update",
    "retail.catalogs.completeQuery",
    "retail.catalogs.import",
    "retail.catalogs.list",
    "retail.catalogs.update",
    "retail.controls.create",
    "retail.controls.delete",
    "retail.controls.export",
    "retail.controls.get",
    "retail.controls.import",
    "retail.controls.list",
    "retail.controls.update",
    "retail.models.create",
    "retail.models.delete",
    "retail.models.list",
    "retail.models.pause",
    "retail.models.resume",
    "retail.models.tune",
    "retail.models.update",
    "retail.operations.get",
    "retail.operations.list",
    "retail.placements.predict",
    "retail.placements.search",
    "retail.products.create",
    "retail.products.delete",
    "retail.products.export",
    "retail.products.get",
    "retail.products.import",
    "retail.products.list",
    "retail.products.purge",
    "retail.products.setSponsorship",
    "retail.products.update",
    "retail.retailProjects.get",
    "retail.servingConfigs.create",
    "retail.servingConfigs.delete",
    "retail.servingConfigs.get",
    "retail.servingConfigs.list",
    "retail.servingConfigs.predict",
    "retail.servingConfigs.search",
    "retail.servingConfigs.update",
    "retail.userEvents.create",
    "retail.userEvents.import",
    "retail.userEvents.purge",
    "retail.userEvents.rejoin",
    "riscconfigurationservice.riscconfigs.createOrUpdate",
    "riscconfigurationservice.riscconfigs.delete",
    "riscconfigurationservice.riscconfigs.get",
    "rma.annotations.create",
    "rma.annotations.get",
    "rma.collectors.create",
    "rma.collectors.delete",
    "rma.collectors.get",
    "rma.collectors.list",
    "rma.collectors.update",
    "rma.locations.get",
    "rma.locations.list",
    "rma.operations.cancel",
    "rma.operations.delete",
    "rma.operations.get",
    "rma.operations.list",
    "run.configurations.get",
    "run.configurations.list",
    "run.executions.delete",
    "run.executions.get",
    "run.executions.list",
    "run.jobs.create",
    "run.jobs.delete",
    "run.jobs.get",
    "run.jobs.getIamPolicy",
    "run.jobs.list",
    "run.jobs.run",
    "run.jobs.setIamPolicy",
    "run.jobs.update",
    "run.locations.list",
    "run.operations.delete",
    "run.operations.get",
    "run.operations.list",
    "run.revisions.delete",
    "run.revisions.get",
    "run.revisions.list",
    "run.routes.get",
    "run.routes.invoke",
    "run.routes.list",
    "run.services.create",
    "run.services.createTagBinding",
    "run.services.delete",
    "run.services.deleteTagBinding",
    "run.services.get",
    "run.services.getIamPolicy",
    "run.services.list",
    "run.services.listEffectiveTags",
    "run.services.listTagBindings",
    "run.services.setIamPolicy",
    "run.services.update",
    "run.tasks.get",
    "run.tasks.list",
    "runapps.applications.create",
    "runapps.applications.delete",
    "runapps.applications.get",
    "runapps.applications.getStatus",
    "runapps.applications.list",
    "runapps.applications.update",
    "runapps.deployments.create",
    "runapps.deployments.get",
    "runapps.deployments.list",
    "runapps.locations.get",
    "runapps.locations.list",
    "runapps.operations.cancel",
    "runapps.operations.delete",
    "runapps.operations.get",
    "runapps.operations.list",
    "runtimeconfig.configs.create",
    "runtimeconfig.configs.delete",
    "runtimeconfig.configs.get",
    "runtimeconfig.configs.getIamPolicy",
    "runtimeconfig.configs.list",
    "runtimeconfig.configs.setIamPolicy",
    "runtimeconfig.configs.update",
    "runtimeconfig.operations.get",
    "runtimeconfig.operations.list",
    "runtimeconfig.variables.create",
    "runtimeconfig.variables.delete",
    "runtimeconfig.variables.get",
    "runtimeconfig.variables.getIamPolicy",
    "runtimeconfig.variables.list",
    "runtimeconfig.variables.setIamPolicy",
    "runtimeconfig.variables.update",
    "runtimeconfig.variables.watch",
    "runtimeconfig.waiters.create",
    "runtimeconfig.waiters.delete",
    "runtimeconfig.waiters.get",
    "runtimeconfig.waiters.getIamPolicy",
    "runtimeconfig.waiters.list",
    "runtimeconfig.waiters.setIamPolicy",
    "runtimeconfig.waiters.update",
    "secretmanager.locations.get",
    "secretmanager.locations.list",
    "secretmanager.secrets.create",
    "secretmanager.secrets.delete",
    "secretmanager.secrets.get",
    "secretmanager.secrets.getIamPolicy",
    "secretmanager.secrets.list",
    "secretmanager.secrets.setIamPolicy",
    "secretmanager.secrets.update",
    "secretmanager.versions.access",
    "secretmanager.versions.add",
    "secretmanager.versions.destroy",
    "secretmanager.versions.disable",
    "secretmanager.versions.enable",
    "secretmanager.versions.get",
    "secretmanager.versions.list",
    "securedlandingzone.overwatches.activate",
    "securedlandingzone.overwatches.create",
    "securedlandingzone.overwatches.delete",
    "securedlandingzone.overwatches.get",
    "securedlandingzone.overwatches.list",
    "securedlandingzone.overwatches.suspend",
    "securedlandingzone.overwatches.update",
    "securitycenter.assets.group",
    "securitycenter.assets.list",
    "securitycenter.assets.listAssetPropertyNames",
    "securitycenter.assets.runDiscovery",
    "securitycenter.assetsecuritymarks.update",
    "securitycenter.bigQueryExports.create",
    "securitycenter.bigQueryExports.delete",
    "securitycenter.bigQueryExports.get",
    "securitycenter.bigQueryExports.list",
    "securitycenter.bigQueryExports.update",
    "securitycenter.containerthreatdetectionsettings.calculate",
    "securitycenter.containerthreatdetectionsettings.get",
    "securitycenter.containerthreatdetectionsettings.update",
    "securitycenter.eventthreatdetectionsettings.calculate",
    "securitycenter.eventthreatdetectionsettings.get",
    "securitycenter.eventthreatdetectionsettings.update",
    "securitycenter.findingexternalsystems.update",
    "securitycenter.findings.bulkMuteUpdate",
    "securitycenter.findings.group",
    "securitycenter.findings.list",
    "securitycenter.findings.listFindingPropertyNames",
    "securitycenter.findings.setMute",
    "securitycenter.findings.setState",
    "securitycenter.findings.setWorkflowState",
    "securitycenter.findings.update",
    "securitycenter.findingsecuritymarks.update",
    "securitycenter.muteconfigs.create",
    "securitycenter.muteconfigs.delete",
    "securitycenter.muteconfigs.get",
    "securitycenter.muteconfigs.list",
    "securitycenter.muteconfigs.update",
    "securitycenter.notificationconfig.create",
    "securitycenter.notificationconfig.delete",
    "securitycenter.notificationconfig.get",
    "securitycenter.notificationconfig.list",
    "securitycenter.notificationconfig.update",
    "securitycenter.rapidvulnerabilitydetectionsettings.calculate",
    "securitycenter.rapidvulnerabilitydetectionsettings.get",
    "securitycenter.rapidvulnerabilitydetectionsettings.update",
    "securitycenter.securitycentersettings.get",
    "securitycenter.securitycentersettings.update",
    "securitycenter.securityhealthanalyticssettings.calculate",
    "securitycenter.securityhealthanalyticssettings.get",
    "securitycenter.securityhealthanalyticssettings.update",
    "securitycenter.sources.get",
    "securitycenter.sources.getIamPolicy",
    "securitycenter.sources.list",
    "securitycenter.sources.setIamPolicy",
    "securitycenter.sources.update",
    "securitycenter.userinterfacemetadata.get",
    "securitycenter.virtualmachinethreatdetectionsettings.calculate",
    "securitycenter.virtualmachinethreatdetectionsettings.get",
    "securitycenter.virtualmachinethreatdetectionsettings.update",
    "securitycenter.websecurityscannersettings.calculate",
    "securitycenter.websecurityscannersettings.get",
    "securitycenter.websecurityscannersettings.update",
    "servicebroker.bindingoperations.get",
    "servicebroker.bindingoperations.list",
    "servicebroker.bindings.create",
    "servicebroker.bindings.delete",
    "servicebroker.bindings.get",
    "servicebroker.bindings.getIamPolicy",
    "servicebroker.bindings.list",
    "servicebroker.bindings.setIamPolicy",
    "servicebroker.catalogs.create",
    "servicebroker.catalogs.delete",
    "servicebroker.catalogs.get",
    "servicebroker.catalogs.getIamPolicy",
    "servicebroker.catalogs.list",
    "servicebroker.catalogs.setIamPolicy",
    "servicebroker.catalogs.validate",
    "servicebroker.instanceoperations.get",
    "servicebroker.instanceoperations.list",
    "servicebroker.instances.create",
    "servicebroker.instances.delete",
    "servicebroker.instances.get",
    "servicebroker.instances.getIamPolicy",
    "servicebroker.instances.list",
    "servicebroker.instances.setIamPolicy",
    "servicebroker.instances.update",
    "serviceconsumermanagement.consumers.get",
    "serviceconsumermanagement.quota.get",
    "serviceconsumermanagement.quota.update",
    "serviceconsumermanagement.tenancyu.addResource",
    "serviceconsumermanagement.tenancyu.create",
    "serviceconsumermanagement.tenancyu.delete",
    "serviceconsumermanagement.tenancyu.list",
    "serviceconsumermanagement.tenancyu.removeResource",
    "servicedirectory.endpoints.create",
    "servicedirectory.endpoints.delete",
    "servicedirectory.endpoints.get",
    "servicedirectory.endpoints.getIamPolicy",
    "servicedirectory.endpoints.list",
    "servicedirectory.endpoints.setIamPolicy",
    "servicedirectory.endpoints.update",
    "servicedirectory.locations.get",
    "servicedirectory.locations.list",
    "servicedirectory.namespaces.associatePrivateZone",
    "servicedirectory.namespaces.create",
    "servicedirectory.namespaces.delete",
    "servicedirectory.namespaces.get",
    "servicedirectory.namespaces.getIamPolicy",
    "servicedirectory.namespaces.list",
    "servicedirectory.namespaces.setIamPolicy",
    "servicedirectory.namespaces.update",
    "servicedirectory.networks.access",
    "servicedirectory.networks.attach",
    "servicedirectory.services.bind",
    "servicedirectory.services.create",
    "servicedirectory.services.delete",
    "servicedirectory.services.get",
    "servicedirectory.services.getIamPolicy",
    "servicedirectory.services.list",
    "servicedirectory.services.resolve",
    "servicedirectory.services.setIamPolicy",
    "servicedirectory.services.update",
    "servicemanagement.services.bind",
    "servicemanagement.services.check",
    "servicemanagement.services.create",
    "servicemanagement.services.delete",
    "servicemanagement.services.get",
    "servicemanagement.services.getIamPolicy",
    "servicemanagement.services.list",
    "servicemanagement.services.quota",
    "servicemanagement.services.report",
    "servicemanagement.services.setIamPolicy",
    "servicemanagement.services.update",
    "servicenetworking.operations.cancel",
    "servicenetworking.operations.delete",
    "servicenetworking.operations.get",
    "servicenetworking.operations.list",
    "servicenetworking.services.addDnsRecordSet",
    "servicenetworking.services.addDnsZone",
    "servicenetworking.services.addPeering",
    "servicenetworking.services.addSubnetwork",
    "servicenetworking.services.createPeeredDnsDomain",
    "servicenetworking.services.deleteConnection",
    "servicenetworking.services.deletePeeredDnsDomain",
    "servicenetworking.services.disableVpcServiceControls",
    "servicenetworking.services.enableVpcServiceControls",
    "servicenetworking.services.get",
    "servicenetworking.services.getConsumerConfig",
    "servicenetworking.services.listPeeredDnsDomains",
    "servicenetworking.services.removeDnsRecordSet",
    "servicenetworking.services.removeDnsZone",
    "servicenetworking.services.updateConsumerConfig",
    "servicenetworking.services.updateDnsRecordSet",
    "servicenetworking.services.use",
    "servicesecurityinsights.clusterSecurityInfo.get",
    "servicesecurityinsights.clusterSecurityInfo.list",
    "servicesecurityinsights.policies.get",
    "servicesecurityinsights.projectStates.get",
    "servicesecurityinsights.securityInfo.list",
    "servicesecurityinsights.securityViews.get",
    "servicesecurityinsights.workloadPolicies.list",
    "servicesecurityinsights.workloadSecurityInfo.get",
    "serviceusage.apiKeys.regenerate",
    "serviceusage.apiKeys.revert",
    "serviceusage.operations.cancel",
    "serviceusage.operations.delete",
    "serviceusage.operations.get",
    "serviceusage.operations.list",
    "serviceusage.quotas.get",
    "serviceusage.quotas.update",
    "serviceusage.services.disable",
    "serviceusage.services.enable",
    "serviceusage.services.get",
    "serviceusage.services.list",
    "serviceusage.services.use",
    "source.repos.create",
    "source.repos.delete",
    "source.repos.get",
    "source.repos.getIamPolicy",
    "source.repos.getProjectConfig",
    "source.repos.list",
    "source.repos.setIamPolicy",
    "source.repos.update",
    "source.repos.updateProjectConfig",
    "source.repos.updateRepoConfig",
    "spanner.backupOperations.cancel",
    "spanner.backupOperations.get",
    "spanner.backupOperations.list",
    "spanner.backups.copy",
    "spanner.backups.create",
    "spanner.backups.delete",
    "spanner.backups.get",
    "spanner.backups.getIamPolicy",
    "spanner.backups.list",
    "spanner.backups.restoreDatabase",
    "spanner.backups.setIamPolicy",
    "spanner.backups.update",
    "spanner.databaseOperations.cancel",
    "spanner.databaseOperations.delete",
    "spanner.databaseOperations.get",
    "spanner.databaseOperations.list",
    "spanner.databaseRoles.list",
    "spanner.databaseRoles.use",
    "spanner.databases.beginOrRollbackReadWriteTransaction",
    "spanner.databases.beginPartitionedDmlTransaction",
    "spanner.databases.beginReadOnlyTransaction",
    "spanner.databases.create",
    "spanner.databases.createBackup",
    "spanner.databases.drop",
    "spanner.databases.get",
    "spanner.databases.getDdl",
    "spanner.databases.getIamPolicy",
    "spanner.databases.list",
    "spanner.databases.partitionQuery",
    "spanner.databases.partitionRead",
    "spanner.databases.read",
    "spanner.databases.select",
    "spanner.databases.setIamPolicy",
    "spanner.databases.update",
    "spanner.databases.updateDdl",
    "spanner.databases.useRoleBasedAccess",
    "spanner.databases.write",
    "spanner.instanceConfigOperations.cancel",
    "spanner.instanceConfigOperations.delete",
    "spanner.instanceConfigOperations.get",
    "spanner.instanceConfigOperations.list",
    "spanner.instanceConfigs.create",
    "spanner.instanceConfigs.delete",
    "spanner.instanceConfigs.get",
    "spanner.instanceConfigs.list",
    "spanner.instanceConfigs.update",
    "spanner.instanceOperations.cancel",
    "spanner.instanceOperations.delete",
    "spanner.instanceOperations.get",
    "spanner.instanceOperations.list",
    "spanner.instances.create",
    "spanner.instances.delete",
    "spanner.instances.get",
    "spanner.instances.getIamPolicy",
    "spanner.instances.list",
    "spanner.instances.setIamPolicy",
    "spanner.instances.update",
    "spanner.sessions.create",
    "spanner.sessions.delete",
    "spanner.sessions.get",
    "spanner.sessions.list",
    "speech.adaptations.execute",
    "speech.config.get",
    "speech.config.update",
    "speech.customClasses.create",
    "speech.customClasses.delete",
    "speech.customClasses.get",
    "speech.customClasses.list",
    "speech.customClasses.undelete",
    "speech.customClasses.update",
    "speech.operations.cancel",
    "speech.operations.delete",
    "speech.operations.get",
    "speech.operations.list",
    "speech.operations.wait",
    "speech.phraseSets.create",
    "speech.phraseSets.delete",
    "speech.phraseSets.get",
    "speech.phraseSets.list",
    "speech.phraseSets.undelete",
    "speech.phraseSets.update",
    "speech.recognizers.create",
    "speech.recognizers.delete",
    "speech.recognizers.get",
    "speech.recognizers.list",
    "speech.recognizers.recognize",
    "speech.recognizers.undelete",
    "speech.recognizers.update",
    "stackdriver.projects.edit",
    "stackdriver.projects.get",
    "stackdriver.resourceMetadata.list",
    "stackdriver.resourceMetadata.write",
    "storage.buckets.create",
    "storage.buckets.createTagBinding",
    "storage.buckets.delete",
    "storage.buckets.deleteTagBinding",
    "storage.buckets.get",
    "storage.buckets.getIamPolicy",
    "storage.buckets.list",
    "storage.buckets.listEffectiveTags",
    "storage.buckets.listTagBindings",
    "storage.buckets.setIamPolicy",
    "storage.buckets.update",
    "storage.hmacKeys.create",
    "storage.hmacKeys.delete",
    "storage.hmacKeys.get",
    "storage.hmacKeys.list",
    "storage.hmacKeys.update",
    "storage.multipartUploads.abort",
    "storage.multipartUploads.create",
    "storage.multipartUploads.list",
    "storage.multipartUploads.listParts",
    "storage.objects.create",
    "storage.objects.delete",
    "storage.objects.get",
    "storage.objects.getIamPolicy",
    "storage.objects.list",
    "storage.objects.setIamPolicy",
    "storage.objects.update",
    "storagetransfer.agentpools.create",
    "storagetransfer.agentpools.delete",
    "storagetransfer.agentpools.get",
    "storagetransfer.agentpools.list",
    "storagetransfer.agentpools.report",
    "storagetransfer.agentpools.update",
    "storagetransfer.jobs.create",
    "storagetransfer.jobs.delete",
    "storagetransfer.jobs.get",
    "storagetransfer.jobs.list",
    "storagetransfer.jobs.run",
    "storagetransfer.jobs.update",
    "storagetransfer.operations.assign",
    "storagetransfer.operations.cancel",
    "storagetransfer.operations.get",
    "storagetransfer.operations.list",
    "storagetransfer.operations.pause",
    "storagetransfer.operations.report",
    "storagetransfer.operations.resume",
    "storagetransfer.projects.getServiceAccount",
    "stream.locations.get",
    "stream.locations.list",
    "stream.operations.cancel",
    "stream.operations.delete",
    "stream.operations.get",
    "stream.operations.list",
    "stream.streamContents.build",
    "stream.streamContents.create",
    "stream.streamContents.delete",
    "stream.streamContents.get",
    "stream.streamContents.list",
    "stream.streamContents.update",
    "stream.streamInstances.create",
    "stream.streamInstances.delete",
    "stream.streamInstances.get",
    "stream.streamInstances.list",
    "stream.streamInstances.rollout",
    "stream.streamInstances.update",
    "subscribewithgoogledeveloper.tools.get",
    "timeseriesinsights.datasets.create",
    "timeseriesinsights.datasets.delete",
    "timeseriesinsights.datasets.evaluate",
    "timeseriesinsights.datasets.list",
    "timeseriesinsights.datasets.query",
    "timeseriesinsights.datasets.update",
    "timeseriesinsights.locations.get",
    "timeseriesinsights.locations.list",
    "tpu.acceleratortypes.get",
    "tpu.acceleratortypes.list",
    "tpu.locations.get",
    "tpu.locations.list",
    "tpu.nodes.create",
    "tpu.nodes.delete",
    "tpu.nodes.get",
    "tpu.nodes.list",
    "tpu.nodes.reimage",
    "tpu.nodes.reset",
    "tpu.nodes.simulateMaintenanceEvent",
    "tpu.nodes.start",
    "tpu.nodes.stop",
    "tpu.nodes.update",
    "tpu.operations.get",
    "tpu.operations.list",
    "tpu.runtimeversions.get",
    "tpu.runtimeversions.list",
    "tpu.tensorflowversions.get",
    "tpu.tensorflowversions.list",
    "trafficdirector.networks.getConfigs",
    "trafficdirector.networks.reportMetrics",
    "transcoder.jobTemplates.create",
    "transcoder.jobTemplates.delete",
    "transcoder.jobTemplates.get",
    "transcoder.jobTemplates.list",
    "transcoder.jobs.create",
    "transcoder.jobs.delete",
    "transcoder.jobs.get",
    "transcoder.jobs.list",
    "transferappliance.appliances.create",
    "transferappliance.appliances.delete",
    "transferappliance.appliances.get",
    "transferappliance.appliances.list",
    "transferappliance.appliances.update",
    "transferappliance.locations.get",
    "transferappliance.locations.list",
    "transferappliance.operations.cancel",
    "transferappliance.operations.delete",
    "transferappliance.operations.get",
    "transferappliance.operations.list",
    "transferappliance.orders.create",
    "transferappliance.orders.delete",
    "transferappliance.orders.get",
    "transferappliance.orders.list",
    "transferappliance.orders.update",
    "translationhub.portals.create",
    "translationhub.portals.delete",
    "translationhub.portals.get",
    "translationhub.portals.list",
    "translationhub.portals.update",
    "videostitcher.cdnKeys.create",
    "videostitcher.cdnKeys.delete",
    "videostitcher.cdnKeys.get",
    "videostitcher.cdnKeys.list",
    "videostitcher.cdnKeys.update",
    "videostitcher.liveAdTagDetails.get",
    "videostitcher.liveAdTagDetails.list",
    "videostitcher.liveSessions.create",
    "videostitcher.liveSessions.get",
    "videostitcher.slates.create",
    "videostitcher.slates.delete",
    "videostitcher.slates.get",
    "videostitcher.slates.list",
    "videostitcher.slates.update",
    "videostitcher.vodAdTagDetails.get",
    "videostitcher.vodAdTagDetails.list",
    "videostitcher.vodSessions.create",
    "videostitcher.vodSessions.get",
    "videostitcher.vodStitchDetails.get",
    "videostitcher.vodStitchDetails.list",
    "visionai.analyses.create",
    "visionai.analyses.delete",
    "visionai.analyses.get",
    "visionai.analyses.getIamPolicy",
    "visionai.analyses.list",
    "visionai.analyses.setIamPolicy",
    "visionai.analyses.update",
    "visionai.annotations.create",
    "visionai.annotations.delete",
    "visionai.annotations.get",
    "visionai.annotations.list",
    "visionai.annotations.update",
    "visionai.applications.create",
    "visionai.applications.delete",
    "visionai.applications.deploy",
    "visionai.applications.get",
    "visionai.applications.list",
    "visionai.applications.undeploy",
    "visionai.applications.update",
    "visionai.assets.clip",
    "visionai.assets.create",
    "visionai.assets.delete",
    "visionai.assets.generateHlsUri",
    "visionai.assets.get",
    "visionai.assets.ingest",
    "visionai.assets.list",
    "visionai.assets.search",
    "visionai.assets.update",
    "visionai.clusters.create",
    "visionai.clusters.delete",
    "visionai.clusters.get",
    "visionai.clusters.getIamPolicy",
    "visionai.clusters.list",
    "visionai.clusters.setIamPolicy",
    "visionai.clusters.update",
    "visionai.clusters.watch",
    "visionai.corpora.create",
    "visionai.corpora.delete",
    "visionai.corpora.get",
    "visionai.corpora.list",
    "visionai.corpora.suggest",
    "visionai.corpora.update",
    "visionai.dataSchemas.create",
    "visionai.dataSchemas.delete",
    "visionai.dataSchemas.get",
    "visionai.dataSchemas.list",
    "visionai.dataSchemas.update",
    "visionai.dataSchemas.validate",
    "visionai.drafts.create",
    "visionai.drafts.delete",
    "visionai.drafts.get",
    "visionai.drafts.list",
    "visionai.drafts.update",
    "visionai.events.create",
    "visionai.events.delete",
    "visionai.events.get",
    "visionai.events.getIamPolicy",
    "visionai.events.list",
    "visionai.events.setIamPolicy",
    "visionai.events.update",
    "visionai.instances.get",
    "visionai.instances.list",
    "visionai.locations.get",
    "visionai.locations.list",
    "visionai.operations.cancel",
    "visionai.operations.delete",
    "visionai.operations.get",
    "visionai.operations.list",
    "visionai.operations.wait",
    "visionai.operators.create",
    "visionai.operators.delete",
    "visionai.operators.get",
    "visionai.operators.getIamPolicy",
    "visionai.operators.list",
    "visionai.operators.setIamPolicy",
    "visionai.operators.update",
    "visionai.processors.create",
    "visionai.processors.delete",
    "visionai.processors.get",
    "visionai.processors.list",
    "visionai.processors.listPrebuilt",
    "visionai.processors.update",
    "visionai.searchConfigs.create",
    "visionai.searchConfigs.delete",
    "visionai.searchConfigs.get",
    "visionai.searchConfigs.list",
    "visionai.searchConfigs.update",
    "visionai.series.acquireLease",
    "visionai.series.create",
    "visionai.series.delete",
    "visionai.series.get",
    "visionai.series.getIamPolicy",
    "visionai.series.list",
    "visionai.series.receive",
    "visionai.series.releaseLease",
    "visionai.series.renewLease",
    "visionai.series.send",
    "visionai.series.setIamPolicy",
    "visionai.series.update",
    "visionai.streams.create",
    "visionai.streams.delete",
    "visionai.streams.get",
    "visionai.streams.getIamPolicy",
    "visionai.streams.list",
    "visionai.streams.receive",
    "visionai.streams.send",
    "visionai.streams.setIamPolicy",
    "visionai.streams.update",
    "visionai.uistreams.create",
    "visionai.uistreams.delete",
    "visionai.uistreams.generateStreamThumbnails",
    "visionai.uistreams.get",
    "visionai.uistreams.list",
    "visualinspection.annotationSets.create",
    "visualinspection.annotationSets.delete",
    "visualinspection.annotationSets.get",
    "visualinspection.annotationSets.list",
    "visualinspection.annotationSets.update",
    "visualinspection.annotationSpecs.create",
    "visualinspection.annotationSpecs.delete",
    "visualinspection.annotationSpecs.get",
    "visualinspection.annotationSpecs.list",
    "visualinspection.annotations.create",
    "visualinspection.annotations.delete",
    "visualinspection.annotations.get",
    "visualinspection.annotations.list",
    "visualinspection.annotations.update",
    "visualinspection.datasets.create",
    "visualinspection.datasets.delete",
    "visualinspection.datasets.export",
    "visualinspection.datasets.get",
    "visualinspection.datasets.import",
    "visualinspection.datasets.list",
    "visualinspection.datasets.update",
    "visualinspection.images.delete",
    "visualinspection.images.get",
    "visualinspection.images.list",
    "visualinspection.images.update",
    "visualinspection.locations.get",
    "visualinspection.locations.list",
    "visualinspection.locations.reportUsageMetrics",
    "visualinspection.modelEvaluations.get",
    "visualinspection.modelEvaluations.list",
    "visualinspection.models.create",
    "visualinspection.models.delete",
    "visualinspection.models.get",
    "visualinspection.models.list",
    "visualinspection.models.update",
    "visualinspection.models.writePrediction",
    "visualinspection.modules.create",
    "visualinspection.modules.delete",
    "visualinspection.modules.get",
    "visualinspection.modules.list",
    "visualinspection.modules.update",
    "visualinspection.operations.get",
    "visualinspection.operations.list",
    "visualinspection.solutionArtifacts.create",
    "visualinspection.solutionArtifacts.delete",
    "visualinspection.solutionArtifacts.get",
    "visualinspection.solutionArtifacts.list",
    "visualinspection.solutionArtifacts.predict",
    "visualinspection.solutionArtifacts.update",
    "visualinspection.solutions.create",
    "visualinspection.solutions.delete",
    "visualinspection.solutions.get",
    "visualinspection.solutions.list",
    "vmmigration.cloneJobs.create",
    "vmmigration.cloneJobs.get",
    "vmmigration.cloneJobs.list",
    "vmmigration.cloneJobs.update",
    "vmmigration.cutoverJobs.create",
    "vmmigration.cutoverJobs.get",
    "vmmigration.cutoverJobs.list",
    "vmmigration.cutoverJobs.update",
    "vmmigration.datacenterConnectors.create",
    "vmmigration.datacenterConnectors.delete",
    "vmmigration.datacenterConnectors.get",
    "vmmigration.datacenterConnectors.list",
    "vmmigration.datacenterConnectors.update",
    "vmmigration.deployments.create",
    "vmmigration.deployments.get",
    "vmmigration.deployments.list",
    "vmmigration.groups.create",
    "vmmigration.groups.delete",
    "vmmigration.groups.get",
    "vmmigration.groups.list",
    "vmmigration.groups.update",
    "vmmigration.locations.get",
    "vmmigration.locations.list",
    "vmmigration.migratingVms.create",
    "vmmigration.migratingVms.delete",
    "vmmigration.migratingVms.get",
    "vmmigration.migratingVms.list",
    "vmmigration.migratingVms.update",
    "vmmigration.operations.cancel",
    "vmmigration.operations.delete",
    "vmmigration.operations.get",
    "vmmigration.operations.list",
    "vmmigration.sources.create",
    "vmmigration.sources.delete",
    "vmmigration.sources.get",
    "vmmigration.sources.list",
    "vmmigration.sources.update",
    "vmmigration.targets.create",
    "vmmigration.targets.delete",
    "vmmigration.targets.get",
    "vmmigration.targets.list",
    "vmmigration.targets.update",
    "vmmigration.utilizationReports.create",
    "vmmigration.utilizationReports.delete",
    "vmmigration.utilizationReports.get",
    "vmmigration.utilizationReports.list",
    "vmwareengine.clusters.create",
    "vmwareengine.clusters.delete",
    "vmwareengine.clusters.get",
    "vmwareengine.clusters.getIamPolicy",
    "vmwareengine.clusters.list",
    "vmwareengine.clusters.setIamPolicy",
    "vmwareengine.clusters.update",
    "vmwareengine.hcxActivationKeys.create",
    "vmwareengine.hcxActivationKeys.get",
    "vmwareengine.hcxActivationKeys.getIamPolicy",
    "vmwareengine.hcxActivationKeys.list",
    "vmwareengine.hcxActivationKeys.setIamPolicy",
    "vmwareengine.locations.get",
    "vmwareengine.locations.list",
    "vmwareengine.networkPolicies.create",
    "vmwareengine.networkPolicies.delete",
    "vmwareengine.networkPolicies.get",
    "vmwareengine.networkPolicies.list",
    "vmwareengine.networkPolicies.update",
    "vmwareengine.nodeTypes.get",
    "vmwareengine.nodeTypes.list",
    "vmwareengine.operations.delete",
    "vmwareengine.operations.get",
    "vmwareengine.operations.list",
    "vmwareengine.privateClouds.create",
    "vmwareengine.privateClouds.delete",
    "vmwareengine.privateClouds.get",
    "vmwareengine.privateClouds.getIamPolicy",
    "vmwareengine.privateClouds.list",
    "vmwareengine.privateClouds.resetNsxCredentials",
    "vmwareengine.privateClouds.resetVcenterCredentials",
    "vmwareengine.privateClouds.setIamPolicy",
    "vmwareengine.privateClouds.showNsxCredentials",
    "vmwareengine.privateClouds.showVcenterCredentials",
    "vmwareengine.privateClouds.undelete",
    "vmwareengine.privateClouds.update",
    "vmwareengine.services.use",
    "vmwareengine.services.view",
    "vmwareengine.subnets.list",
    "vmwareengine.vmwareEngineNetworks.create",
    "vmwareengine.vmwareEngineNetworks.delete",
    "vmwareengine.vmwareEngineNetworks.get",
    "vmwareengine.vmwareEngineNetworks.list",
    "vmwareengine.vmwareEngineNetworks.update",
    "vpcaccess.connectors.create",
    "vpcaccess.connectors.delete",
    "vpcaccess.connectors.get",
    "vpcaccess.connectors.list",
    "vpcaccess.connectors.use",
    "vpcaccess.locations.list",
    "vpcaccess.operations.get",
    "vpcaccess.operations.list",
    "workflows.callbacks.send",
    "workflows.executions.cancel",
    "workflows.executions.create",
    "workflows.executions.get",
    "workflows.executions.list",
    "workflows.locations.get",
    "workflows.locations.list",
    "workflows.operations.cancel",
    "workflows.operations.get",
    "workflows.operations.list",
    "workflows.workflows.create",
    "workflows.workflows.delete",
    "workflows.workflows.get",
    "workflows.workflows.list",
    "workflows.workflows.update",
    "workloadmanager.evaluations.create",
    "workloadmanager.evaluations.delete",
    "workloadmanager.evaluations.get",
    "workloadmanager.evaluations.list",
    "workloadmanager.evaluations.run",
    "workloadmanager.evaluations.update",
    "workloadmanager.executions.delete",
    "workloadmanager.executions.get",
    "workloadmanager.executions.list",
    "workloadmanager.locations.get",
    "workloadmanager.locations.list",
    "workloadmanager.operations.cancel",
    "workloadmanager.operations.delete",
    "workloadmanager.operations.get",
    "workloadmanager.operations.list",
    "workloadmanager.results.list",
    "workloadmanager.rules.list",
    "workstations.operations.get",
    "workstations.workstationClusters.create",
    "workstations.workstationClusters.delete",
    "workstations.workstationClusters.get",
    "workstations.workstationClusters.list",
    "workstations.workstationClusters.update",
    "workstations.workstationConfigs.create",
    "workstations.workstationConfigs.delete",
    "workstations.workstationConfigs.get",
    "workstations.workstationConfigs.getIamPolicy",
    "workstations.workstationConfigs.list",
    "workstations.workstationConfigs.setIamPolicy",
    "workstations.workstationConfigs.update",
    "workstations.workstations.create",
    "workstations.workstations.delete",
    "workstations.workstations.get",
    "workstations.workstations.getIamPolicy",
    "workstations.workstations.list",
    "workstations.workstations.setIamPolicy",
    "workstations.workstations.start",
    "workstations.workstations.stop",
    "workstations.workstations.update",
    "workstations.workstations.use"
]