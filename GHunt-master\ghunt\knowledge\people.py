# https://developers.google.com/people/api/rest/v1/people#usertype
user_types = {
    "USER_TYPE_UNKNOWN": "The user type is not known.", # Official
    "GOOGLE_USER": "The user is a Google user.", # Official
    "GPLUS_USER": "The user is a Currents user.", # Official
    "GOOGLE_APPS_USER": "The user is a Google Workspace user.", # Official
    "OWNER_USER_TYPE_UNKNOWN": "The user type is not known.", # Guess
    "GPLUS_DISABLED_BY_ADMIN": "This user's Currents account has been disabled by an admin.", # Guess
    "GOOGLE_APPS_USER": "The user is a Google Apps user.", # Guess
    "GOOGLE_FAMILY_USER": "The user is a Google Family user.", # Guess
    "GOOGLE_FAMILY_CHILD_USER": "The user is a Google Family child user.", # Guess
    "GOOGLE_APPS_ADMIN_DISABLED": "This admin of Google Apps has been disabled.", # Guess
    "GOOGLE_ONE_USER": "The user is a Google One user.", # Guess
    "GOOGLE_FAMILY_CONVERTED_CHILD_USER": "This Google Family user was converted to a child user." # Guess
}