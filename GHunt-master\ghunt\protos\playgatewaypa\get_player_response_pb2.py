# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: definitions/get_player_response.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n%definitions/get_player_response.proto\"\xa4\"\n\x16GetPlayerResponseProto\x12\x33\n\x06\x66ield1\x18\x01 \x01(\x0b\x32#.GetPlayerResponseProto.field1_type\x1a\xd4!\n\x0b\x66ield1_type\x12@\n\x07results\x18\x85\xf2\xc3\x90\x01 \x01(\x0b\x32+.GetPlayerResponseProto.field1_type.Results\x1a\x82!\n\x07Results\x12\x42\n\x06player\x18\x01 \x01(\x0b\x32\x32.GetPlayerResponseProto.field1_type.Results.Player\x12\x44\n\x07section\x18\x02 \x03(\x0b\x32\x33.GetPlayerResponseProto.field1_type.Results.Section\x1a\xd7\x01\n\x06Player\x12N\n\x06\x66ield1\x18\x01 \x01(\x0b\x32>.GetPlayerResponseProto.field1_type.Results.Player.field1_type\x12I\n\x06\x61vatar\x18\x02 \x01(\x0b\x32\x39.GetPlayerResponseProto.field1_type.Results.Player.Avatar\x1a\x1b\n\x0b\x66ield1_type\x12\x0c\n\x04name\x18\x01 \x01(\t\x1a\x15\n\x06\x41vatar\x12\x0b\n\x03url\x18\x01 \x01(\t\x1a\x92\x1e\n\x07Section\x12L\n\x07\x63ounter\x18\x02 \x01(\x0b\x32;.GetPlayerResponseProto.field1_type.Results.Section.Counter\x12O\n\x06\x66ield3\x18\x03 \x01(\x0b\x32?.GetPlayerResponseProto.field1_type.Results.Section.field3_type\x12U\n\x0cplayed_games\x18\x05 \x01(\x0b\x32?.GetPlayerResponseProto.field1_type.Results.Section.PlayedGames\x12V\n\x0c\x61\x63hievements\x18\x06 \x01(\x0b\x32@.GetPlayerResponseProto.field1_type.Results.Section.Achievements\x1a\x19\n\x07\x43ounter\x12\x0e\n\x06number\x18\x01 \x01(\t\x1a#\n\x0b\x66ield3_type\x12\x14\n\x0csection_name\x18\x01 \x01(\t\x1a\xe1\r\n\x0bPlayedGames\x12[\n\x06\x66ield1\x18\x01 \x01(\x0b\x32K.GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type\x1a\xf4\x0c\n\x0b\x66ield1_type\x12g\n\x06\x66ield1\x18\x01 \x01(\x0b\x32W.GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.field1_type\x12\x61\n\x04game\x18\xea\xbd\x9e\x63 \x03(\x0b\x32P.GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game\x12z\n\x0e\x66ield234686954\x18\xea\x93\xf4o \x01(\x0b\x32_.GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.field234686954_type\x1a\xbd\x01\n\x0b\x66ield1_type\x12\x86\x01\n\x0e\x66ield203130867\x18\xf3\x8f\xee` \x01(\x0b\x32k.GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.field1_type.field203130867_type\x1a%\n\x13\x66ield203130867_type\x12\x0e\n\x06\x66ield1\x18\x01 \x01(\t\x1a\xfe\x05\n\x04Game\x12\x63\n\x04icon\x18\x03 \x01(\x0b\x32U.GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game.Icon\x12l\n\x06\x66ield6\x18\x06 \x01(\x0b\x32\\.GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game.field6_type\x1a\x13\n\x04Icon\x12\x0b\n\x03url\x18\x01 \x01(\t\x1a\x8d\x04\n\x0b\x66ield6_type\x12x\n\x06\x66ield1\x18\x01 \x01(\x0b\x32h.GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game.field6_type.field1_type\x1a\x83\x03\n\x0b\x66ield1_type\x12\x84\x01\n\x07\x64\x65tails\x18\xb6\xaa\xa8^ \x01(\x0b\x32p.GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game.field6_type.field1_type.Details\x1a\xec\x01\n\x07\x44\x65tails\x12\x8c\x01\n\x06\x66ield1\x18\x01 \x01(\x0b\x32|.GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game.field6_type.field1_type.Details.field1_type\x12\r\n\x05title\x18\x02 \x01(\t\x12\x0e\n\x06\x65\x64itor\x18\x03 \x01(\t\x1a\x33\n\x0b\x66ield1_type\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\x12\x14\n\x0cpackage_name\x18\x02 \x01(\t\x1a\xdb\x02\n\x13\x66ield234686954_type\x12{\n\x06\x66ield1\x18\x01 \x01(\x0b\x32k.GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.field234686954_type.field1_type\x1a\xc6\x01\n\x0b\x66ield1_type\x12\x0e\n\x06\x66ield1\x18\x01 \x01(\t\x12\x87\x01\n\x06\x66ield2\x18\x02 \x01(\x0b\x32w.GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.field234686954_type.field1_type.field2_type\x1a\x1d\n\x0b\x66ield2_type\x12\x0e\n\x06\x66ield2\x18\x02 \x01(\x03\x1a\x94\r\n\x0c\x41\x63hievements\x12\\\n\x06\x66ield1\x18\x01 \x01(\x0b\x32L.GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type\x1a\xa5\x0c\n\x0b\x66ield1_type\x12h\n\x06\x66ield3\x18\x03 \x01(\x0b\x32X.GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type\x12\x0e\n\x06\x66ield4\x18\x04 \x01(\x03\x1a\x9b\x0b\n\x0b\x66ield3_type\x12t\n\x06\x66ield1\x18\x01 \x01(\x0b\x32\x64.GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field1_type\x12t\n\x06\x66ield2\x18\x02 \x01(\x0b\x32\x64.GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field2_type\x1a\xbe\x06\n\x0b\x66ield1_type\x12\x89\x01\n\x0b\x61\x63hievement\x18\xb8\x81\xf8\x90\x01 \x03(\x0b\x32p.GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field1_type.Achievement\x1a\xa2\x05\n\x0b\x41\x63hievement\x12\x8c\x01\n\x06\x66ield7\x18\x07 \x01(\x0b\x32|.GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field1_type.Achievement.field7_type\x1a\x83\x04\n\x0b\x66ield7_type\x12\x99\x01\n\x06\x66ield1\x18\x01 \x01(\x0b\x32\x88\x01.GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field1_type.Achievement.field7_type.field1_type\x1a\xd7\x02\n\x0b\x66ield1_type\x12\xa6\x01\n\x07\x64\x65tails\x18\xf2\xa2\x86\x92\x01 \x01(\x0b\x32\x90\x01.GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field1_type.Achievement.field7_type.field1_type.Details\x1a\x9e\x01\n\x07\x44\x65tails\x12\r\n\x05title\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\n\n\x02xp\x18\x03 \x01(\x03\x12\n\n\x02id\x18\x04 \x01(\x07\x12\x10\n\x08icon_url\x18\x05 \x01(\t\x12\x0f\n\x07game_id\x18\x06 \x01(\t\x12\x11\n\ttimestamp\x18\x07 \x01(\x03\x12\x0e\n\x06\x66ield8\x18\x08 \x01(\x03\x12\x11\n\tis_secret\x18\x0c \x01(\x03\x1a\xde\x02\n\x0b\x66ield2_type\x12\x80\x01\n\x06\x66ield1\x18\x01 \x01(\x0b\x32p.GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field2_type.field1_type\x1a\xcb\x01\n\x0b\x66ield1_type\x12\x87\x01\n\x04page\x18\xcf\xa7\xd3\x90\x01 \x01(\x0b\x32u.GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field2_type.field1_type.Page\x1a\x32\n\x04Page\x12\x11\n\tplayer_id\x18\x01 \x01(\t\x12\x17\n\x0fnext_page_token\x18\x03 \x01(\tb\x06proto3')



_GETPLAYERRESPONSEPROTO = DESCRIPTOR.message_types_by_name['GetPlayerResponseProto']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE = _GETPLAYERRESPONSEPROTO.nested_types_by_name['field1_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE.nested_types_by_name['Results']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_PLAYER = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS.nested_types_by_name['Player']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_PLAYER_FIELD1_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_PLAYER.nested_types_by_name['field1_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_PLAYER_AVATAR = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_PLAYER.nested_types_by_name['Avatar']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS.nested_types_by_name['Section']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_COUNTER = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION.nested_types_by_name['Counter']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_FIELD3_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION.nested_types_by_name['field3_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION.nested_types_by_name['PlayedGames']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES.nested_types_by_name['field1_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD1_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE.nested_types_by_name['field1_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD1_TYPE_FIELD203130867_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD1_TYPE.nested_types_by_name['field203130867_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE.nested_types_by_name['Game']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_ICON = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME.nested_types_by_name['Icon']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME.nested_types_by_name['field6_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE_FIELD1_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE.nested_types_by_name['field1_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE_FIELD1_TYPE_DETAILS = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE_FIELD1_TYPE.nested_types_by_name['Details']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE_FIELD1_TYPE_DETAILS_FIELD1_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE_FIELD1_TYPE_DETAILS.nested_types_by_name['field1_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD234686954_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE.nested_types_by_name['field234686954_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD234686954_TYPE_FIELD1_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD234686954_TYPE.nested_types_by_name['field1_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD234686954_TYPE_FIELD1_TYPE_FIELD2_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD234686954_TYPE_FIELD1_TYPE.nested_types_by_name['field2_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION.nested_types_by_name['Achievements']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS.nested_types_by_name['field1_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE.nested_types_by_name['field3_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE.nested_types_by_name['field1_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE.nested_types_by_name['Achievement']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT_FIELD7_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT.nested_types_by_name['field7_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT_FIELD7_TYPE_FIELD1_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT_FIELD7_TYPE.nested_types_by_name['field1_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT_FIELD7_TYPE_FIELD1_TYPE_DETAILS = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT_FIELD7_TYPE_FIELD1_TYPE.nested_types_by_name['Details']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD2_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE.nested_types_by_name['field2_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD2_TYPE_FIELD1_TYPE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD2_TYPE.nested_types_by_name['field1_type']
_GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD2_TYPE_FIELD1_TYPE_PAGE = _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD2_TYPE_FIELD1_TYPE.nested_types_by_name['Page']
GetPlayerResponseProto = _reflection.GeneratedProtocolMessageType('GetPlayerResponseProto', (_message.Message,), {

  'field1_type' : _reflection.GeneratedProtocolMessageType('field1_type', (_message.Message,), {

    'Results' : _reflection.GeneratedProtocolMessageType('Results', (_message.Message,), {

      'Player' : _reflection.GeneratedProtocolMessageType('Player', (_message.Message,), {

        'field1_type' : _reflection.GeneratedProtocolMessageType('field1_type', (_message.Message,), {
          'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_PLAYER_FIELD1_TYPE,
          '__module__' : 'definitions.get_player_response_pb2'
          # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Player.field1_type)
          })
        ,

        'Avatar' : _reflection.GeneratedProtocolMessageType('Avatar', (_message.Message,), {
          'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_PLAYER_AVATAR,
          '__module__' : 'definitions.get_player_response_pb2'
          # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Player.Avatar)
          })
        ,
        'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_PLAYER,
        '__module__' : 'definitions.get_player_response_pb2'
        # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Player)
        })
      ,

      'Section' : _reflection.GeneratedProtocolMessageType('Section', (_message.Message,), {

        'Counter' : _reflection.GeneratedProtocolMessageType('Counter', (_message.Message,), {
          'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_COUNTER,
          '__module__' : 'definitions.get_player_response_pb2'
          # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.Counter)
          })
        ,

        'field3_type' : _reflection.GeneratedProtocolMessageType('field3_type', (_message.Message,), {
          'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_FIELD3_TYPE,
          '__module__' : 'definitions.get_player_response_pb2'
          # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.field3_type)
          })
        ,

        'PlayedGames' : _reflection.GeneratedProtocolMessageType('PlayedGames', (_message.Message,), {

          'field1_type' : _reflection.GeneratedProtocolMessageType('field1_type', (_message.Message,), {

            'field1_type' : _reflection.GeneratedProtocolMessageType('field1_type', (_message.Message,), {

              'field203130867_type' : _reflection.GeneratedProtocolMessageType('field203130867_type', (_message.Message,), {
                'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD1_TYPE_FIELD203130867_TYPE,
                '__module__' : 'definitions.get_player_response_pb2'
                # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.field1_type.field203130867_type)
                })
              ,
              'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD1_TYPE,
              '__module__' : 'definitions.get_player_response_pb2'
              # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.field1_type)
              })
            ,

            'Game' : _reflection.GeneratedProtocolMessageType('Game', (_message.Message,), {

              'Icon' : _reflection.GeneratedProtocolMessageType('Icon', (_message.Message,), {
                'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_ICON,
                '__module__' : 'definitions.get_player_response_pb2'
                # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game.Icon)
                })
              ,

              'field6_type' : _reflection.GeneratedProtocolMessageType('field6_type', (_message.Message,), {

                'field1_type' : _reflection.GeneratedProtocolMessageType('field1_type', (_message.Message,), {

                  'Details' : _reflection.GeneratedProtocolMessageType('Details', (_message.Message,), {

                    'field1_type' : _reflection.GeneratedProtocolMessageType('field1_type', (_message.Message,), {
                      'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE_FIELD1_TYPE_DETAILS_FIELD1_TYPE,
                      '__module__' : 'definitions.get_player_response_pb2'
                      # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game.field6_type.field1_type.Details.field1_type)
                      })
                    ,
                    'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE_FIELD1_TYPE_DETAILS,
                    '__module__' : 'definitions.get_player_response_pb2'
                    # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game.field6_type.field1_type.Details)
                    })
                  ,
                  'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE_FIELD1_TYPE,
                  '__module__' : 'definitions.get_player_response_pb2'
                  # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game.field6_type.field1_type)
                  })
                ,
                'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE,
                '__module__' : 'definitions.get_player_response_pb2'
                # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game.field6_type)
                })
              ,
              'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME,
              '__module__' : 'definitions.get_player_response_pb2'
              # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game)
              })
            ,

            'field234686954_type' : _reflection.GeneratedProtocolMessageType('field234686954_type', (_message.Message,), {

              'field1_type' : _reflection.GeneratedProtocolMessageType('field1_type', (_message.Message,), {

                'field2_type' : _reflection.GeneratedProtocolMessageType('field2_type', (_message.Message,), {
                  'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD234686954_TYPE_FIELD1_TYPE_FIELD2_TYPE,
                  '__module__' : 'definitions.get_player_response_pb2'
                  # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.field234686954_type.field1_type.field2_type)
                  })
                ,
                'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD234686954_TYPE_FIELD1_TYPE,
                '__module__' : 'definitions.get_player_response_pb2'
                # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.field234686954_type.field1_type)
                })
              ,
              'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD234686954_TYPE,
              '__module__' : 'definitions.get_player_response_pb2'
              # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.field234686954_type)
              })
            ,
            'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE,
            '__module__' : 'definitions.get_player_response_pb2'
            # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type)
            })
          ,
          'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES,
          '__module__' : 'definitions.get_player_response_pb2'
          # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.PlayedGames)
          })
        ,

        'Achievements' : _reflection.GeneratedProtocolMessageType('Achievements', (_message.Message,), {

          'field1_type' : _reflection.GeneratedProtocolMessageType('field1_type', (_message.Message,), {

            'field3_type' : _reflection.GeneratedProtocolMessageType('field3_type', (_message.Message,), {

              'field1_type' : _reflection.GeneratedProtocolMessageType('field1_type', (_message.Message,), {

                'Achievement' : _reflection.GeneratedProtocolMessageType('Achievement', (_message.Message,), {

                  'field7_type' : _reflection.GeneratedProtocolMessageType('field7_type', (_message.Message,), {

                    'field1_type' : _reflection.GeneratedProtocolMessageType('field1_type', (_message.Message,), {

                      'Details' : _reflection.GeneratedProtocolMessageType('Details', (_message.Message,), {
                        'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT_FIELD7_TYPE_FIELD1_TYPE_DETAILS,
                        '__module__' : 'definitions.get_player_response_pb2'
                        # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field1_type.Achievement.field7_type.field1_type.Details)
                        })
                      ,
                      'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT_FIELD7_TYPE_FIELD1_TYPE,
                      '__module__' : 'definitions.get_player_response_pb2'
                      # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field1_type.Achievement.field7_type.field1_type)
                      })
                    ,
                    'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT_FIELD7_TYPE,
                    '__module__' : 'definitions.get_player_response_pb2'
                    # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field1_type.Achievement.field7_type)
                    })
                  ,
                  'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT,
                  '__module__' : 'definitions.get_player_response_pb2'
                  # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field1_type.Achievement)
                  })
                ,
                'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE,
                '__module__' : 'definitions.get_player_response_pb2'
                # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field1_type)
                })
              ,

              'field2_type' : _reflection.GeneratedProtocolMessageType('field2_type', (_message.Message,), {

                'field1_type' : _reflection.GeneratedProtocolMessageType('field1_type', (_message.Message,), {

                  'Page' : _reflection.GeneratedProtocolMessageType('Page', (_message.Message,), {
                    'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD2_TYPE_FIELD1_TYPE_PAGE,
                    '__module__' : 'definitions.get_player_response_pb2'
                    # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field2_type.field1_type.Page)
                    })
                  ,
                  'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD2_TYPE_FIELD1_TYPE,
                  '__module__' : 'definitions.get_player_response_pb2'
                  # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field2_type.field1_type)
                  })
                ,
                'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD2_TYPE,
                '__module__' : 'definitions.get_player_response_pb2'
                # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field2_type)
                })
              ,
              'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE,
              '__module__' : 'definitions.get_player_response_pb2'
              # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type)
              })
            ,
            'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE,
            '__module__' : 'definitions.get_player_response_pb2'
            # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type)
            })
          ,
          'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS,
          '__module__' : 'definitions.get_player_response_pb2'
          # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section.Achievements)
          })
        ,
        'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION,
        '__module__' : 'definitions.get_player_response_pb2'
        # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results.Section)
        })
      ,
      'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS,
      '__module__' : 'definitions.get_player_response_pb2'
      # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type.Results)
      })
    ,
    'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO_FIELD1_TYPE,
    '__module__' : 'definitions.get_player_response_pb2'
    # @@protoc_insertion_point(class_scope:GetPlayerResponseProto.field1_type)
    })
  ,
  'DESCRIPTOR' : _GETPLAYERRESPONSEPROTO,
  '__module__' : 'definitions.get_player_response_pb2'
  # @@protoc_insertion_point(class_scope:GetPlayerResponseProto)
  })
_sym_db.RegisterMessage(GetPlayerResponseProto)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Player)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Player.field1_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Player.Avatar)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.Counter)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.field3_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.PlayedGames)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.field1_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.field1_type.field203130867_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game.Icon)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game.field6_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game.field6_type.field1_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game.field6_type.field1_type.Details)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.Game.field6_type.field1_type.Details.field1_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.field234686954_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.field234686954_type.field1_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.PlayedGames.field1_type.field234686954_type.field1_type.field2_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.Achievements)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field1_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field1_type.Achievement)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field1_type.Achievement.field7_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field1_type.Achievement.field7_type.field1_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field1_type.Achievement.field7_type.field1_type.Details)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field2_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field2_type.field1_type)
_sym_db.RegisterMessage(GetPlayerResponseProto.field1_type.Results.Section.Achievements.field1_type.field3_type.field2_type.field1_type.Page)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _GETPLAYERRESPONSEPROTO._serialized_start=42
  _GETPLAYERRESPONSEPROTO._serialized_end=4430
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE._serialized_start=122
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE._serialized_end=4430
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS._serialized_start=204
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS._serialized_end=4430
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_PLAYER._serialized_start=354
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_PLAYER._serialized_end=569
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_PLAYER_FIELD1_TYPE._serialized_start=519
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_PLAYER_FIELD1_TYPE._serialized_end=546
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_PLAYER_AVATAR._serialized_start=548
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_PLAYER_AVATAR._serialized_end=569
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION._serialized_start=572
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION._serialized_end=4430
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_COUNTER._serialized_start=917
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_COUNTER._serialized_end=942
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_FIELD3_TYPE._serialized_start=944
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_FIELD3_TYPE._serialized_end=979
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES._serialized_start=982
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES._serialized_end=2743
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE._serialized_start=1091
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE._serialized_end=2743
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD1_TYPE._serialized_start=1435
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD1_TYPE._serialized_end=1624
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD1_TYPE_FIELD203130867_TYPE._serialized_start=1587
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD1_TYPE_FIELD203130867_TYPE._serialized_end=1624
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME._serialized_start=1627
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME._serialized_end=2393
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_ICON._serialized_start=1846
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_ICON._serialized_end=1865
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE._serialized_start=1868
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE._serialized_end=2393
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE_FIELD1_TYPE._serialized_start=2006
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE_FIELD1_TYPE._serialized_end=2393
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE_FIELD1_TYPE_DETAILS._serialized_start=2157
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE_FIELD1_TYPE_DETAILS._serialized_end=2393
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE_FIELD1_TYPE_DETAILS_FIELD1_TYPE._serialized_start=2342
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_GAME_FIELD6_TYPE_FIELD1_TYPE_DETAILS_FIELD1_TYPE._serialized_end=2393
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD234686954_TYPE._serialized_start=2396
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD234686954_TYPE._serialized_end=2743
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD234686954_TYPE_FIELD1_TYPE._serialized_start=2545
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD234686954_TYPE_FIELD1_TYPE._serialized_end=2743
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD234686954_TYPE_FIELD1_TYPE_FIELD2_TYPE._serialized_start=2714
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_PLAYEDGAMES_FIELD1_TYPE_FIELD234686954_TYPE_FIELD1_TYPE_FIELD2_TYPE._serialized_end=2743
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS._serialized_start=2746
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS._serialized_end=4430
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE._serialized_start=2857
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE._serialized_end=4430
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE._serialized_start=2995
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE._serialized_end=4430
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE._serialized_start=3247
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE._serialized_end=4077
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT._serialized_start=3403
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT._serialized_end=4077
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT_FIELD7_TYPE._serialized_start=3562
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT_FIELD7_TYPE._serialized_end=4077
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT_FIELD7_TYPE_FIELD1_TYPE._serialized_start=3734
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT_FIELD7_TYPE_FIELD1_TYPE._serialized_end=4077
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT_FIELD7_TYPE_FIELD1_TYPE_DETAILS._serialized_start=3919
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD1_TYPE_ACHIEVEMENT_FIELD7_TYPE_FIELD1_TYPE_DETAILS._serialized_end=4077
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD2_TYPE._serialized_start=4080
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD2_TYPE._serialized_end=4430
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD2_TYPE_FIELD1_TYPE._serialized_start=4227
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD2_TYPE_FIELD1_TYPE._serialized_end=4430
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD2_TYPE_FIELD1_TYPE_PAGE._serialized_start=4380
  _GETPLAYERRESPONSEPROTO_FIELD1_TYPE_RESULTS_SECTION_ACHIEVEMENTS_FIELD1_TYPE_FIELD3_TYPE_FIELD2_TYPE_FIELD1_TYPE_PAGE._serialized_end=4430
# @@protoc_insertion_point(module_scope)
