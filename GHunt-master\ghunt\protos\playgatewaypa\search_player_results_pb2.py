# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: definitions/search_player_results.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'definitions/search_player_results.proto\"\xe6\x05\n\x18PlayerSearchResultsProto\x12\x35\n\x06\x66ield1\x18\x01 \x01(\x0b\x32%.PlayerSearchResultsProto.field1_type\x1a\x92\x05\n\x0b\x66ield1_type\x12\x41\n\x07results\x18\xc6\xa2\xe5[ \x01(\x0b\x32-.PlayerSearchResultsProto.field1_type.Results\x1a\xbf\x04\n\x07Results\x12I\n\x06\x66ield1\x18\x01 \x01(\x0b\x32\x39.PlayerSearchResultsProto.field1_type.Results.field1_type\x1a\xe8\x03\n\x0b\x66ield1_type\x12U\n\x06\x66ield1\x18\x01 \x01(\x0b\x32\x45.PlayerSearchResultsProto.field1_type.Results.field1_type.field1_type\x1a\x81\x03\n\x0b\x66ield1_type\x12_\n\x06player\x18\xda\xe7\x8bo \x03(\x0b\x32L.PlayerSearchResultsProto.field1_type.Results.field1_type.field1_type.Player\x1a\x90\x02\n\x06Player\x12\x63\n\x06\x61vatar\x18\x03 \x01(\x0b\x32S.PlayerSearchResultsProto.field1_type.Results.field1_type.field1_type.Player.Avatar\x12\x65\n\x07\x61\x63\x63ount\x18\x06 \x01(\x0b\x32T.PlayerSearchResultsProto.field1_type.Results.field1_type.field1_type.Player.Account\x1a\x15\n\x06\x41vatar\x12\x0b\n\x03url\x18\x01 \x01(\t\x1a#\n\x07\x41\x63\x63ount\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\tb\x06proto3')



_PLAYERSEARCHRESULTSPROTO = DESCRIPTOR.message_types_by_name['PlayerSearchResultsProto']
_PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE = _PLAYERSEARCHRESULTSPROTO.nested_types_by_name['field1_type']
_PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS = _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE.nested_types_by_name['Results']
_PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE = _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS.nested_types_by_name['field1_type']
_PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE = _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE.nested_types_by_name['field1_type']
_PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE_PLAYER = _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE.nested_types_by_name['Player']
_PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE_PLAYER_AVATAR = _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE_PLAYER.nested_types_by_name['Avatar']
_PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE_PLAYER_ACCOUNT = _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE_PLAYER.nested_types_by_name['Account']
PlayerSearchResultsProto = _reflection.GeneratedProtocolMessageType('PlayerSearchResultsProto', (_message.Message,), {

  'field1_type' : _reflection.GeneratedProtocolMessageType('field1_type', (_message.Message,), {

    'Results' : _reflection.GeneratedProtocolMessageType('Results', (_message.Message,), {

      'field1_type' : _reflection.GeneratedProtocolMessageType('field1_type', (_message.Message,), {

        'field1_type' : _reflection.GeneratedProtocolMessageType('field1_type', (_message.Message,), {

          'Player' : _reflection.GeneratedProtocolMessageType('Player', (_message.Message,), {

            'Avatar' : _reflection.GeneratedProtocolMessageType('Avatar', (_message.Message,), {
              'DESCRIPTOR' : _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE_PLAYER_AVATAR,
              '__module__' : 'definitions.search_player_results_pb2'
              # @@protoc_insertion_point(class_scope:PlayerSearchResultsProto.field1_type.Results.field1_type.field1_type.Player.Avatar)
              })
            ,

            'Account' : _reflection.GeneratedProtocolMessageType('Account', (_message.Message,), {
              'DESCRIPTOR' : _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE_PLAYER_ACCOUNT,
              '__module__' : 'definitions.search_player_results_pb2'
              # @@protoc_insertion_point(class_scope:PlayerSearchResultsProto.field1_type.Results.field1_type.field1_type.Player.Account)
              })
            ,
            'DESCRIPTOR' : _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE_PLAYER,
            '__module__' : 'definitions.search_player_results_pb2'
            # @@protoc_insertion_point(class_scope:PlayerSearchResultsProto.field1_type.Results.field1_type.field1_type.Player)
            })
          ,
          'DESCRIPTOR' : _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE,
          '__module__' : 'definitions.search_player_results_pb2'
          # @@protoc_insertion_point(class_scope:PlayerSearchResultsProto.field1_type.Results.field1_type.field1_type)
          })
        ,
        'DESCRIPTOR' : _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE,
        '__module__' : 'definitions.search_player_results_pb2'
        # @@protoc_insertion_point(class_scope:PlayerSearchResultsProto.field1_type.Results.field1_type)
        })
      ,
      'DESCRIPTOR' : _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS,
      '__module__' : 'definitions.search_player_results_pb2'
      # @@protoc_insertion_point(class_scope:PlayerSearchResultsProto.field1_type.Results)
      })
    ,
    'DESCRIPTOR' : _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE,
    '__module__' : 'definitions.search_player_results_pb2'
    # @@protoc_insertion_point(class_scope:PlayerSearchResultsProto.field1_type)
    })
  ,
  'DESCRIPTOR' : _PLAYERSEARCHRESULTSPROTO,
  '__module__' : 'definitions.search_player_results_pb2'
  # @@protoc_insertion_point(class_scope:PlayerSearchResultsProto)
  })
_sym_db.RegisterMessage(PlayerSearchResultsProto)
_sym_db.RegisterMessage(PlayerSearchResultsProto.field1_type)
_sym_db.RegisterMessage(PlayerSearchResultsProto.field1_type.Results)
_sym_db.RegisterMessage(PlayerSearchResultsProto.field1_type.Results.field1_type)
_sym_db.RegisterMessage(PlayerSearchResultsProto.field1_type.Results.field1_type.field1_type)
_sym_db.RegisterMessage(PlayerSearchResultsProto.field1_type.Results.field1_type.field1_type.Player)
_sym_db.RegisterMessage(PlayerSearchResultsProto.field1_type.Results.field1_type.field1_type.Player.Avatar)
_sym_db.RegisterMessage(PlayerSearchResultsProto.field1_type.Results.field1_type.field1_type.Player.Account)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _PLAYERSEARCHRESULTSPROTO._serialized_start=44
  _PLAYERSEARCHRESULTSPROTO._serialized_end=786
  _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE._serialized_start=128
  _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE._serialized_end=786
  _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS._serialized_start=211
  _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS._serialized_end=786
  _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE._serialized_start=298
  _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE._serialized_end=786
  _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE._serialized_start=401
  _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE._serialized_end=786
  _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE_PLAYER._serialized_start=514
  _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE_PLAYER._serialized_end=786
  _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE_PLAYER_AVATAR._serialized_start=728
  _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE_PLAYER_AVATAR._serialized_end=749
  _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE_PLAYER_ACCOUNT._serialized_start=751
  _PLAYERSEARCHRESULTSPROTO_FIELD1_TYPE_RESULTS_FIELD1_TYPE_FIELD1_TYPE_PLAYER_ACCOUNT._serialized_end=786
# @@protoc_insertion_point(module_scope)
