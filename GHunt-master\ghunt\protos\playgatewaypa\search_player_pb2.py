# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: definitions/search_player.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1f\x64\x65\x66initions/search_player.proto\"\xa3\x01\n\x11PlayerSearchProto\x12\x32\n\x0bsearch_form\x18\x02 \x01(\x0b\x32\x1d.PlayerSearchProto.SearchForm\x1aZ\n\nSearchForm\x12\x35\n\x05query\x18\x9c\xe7\xa7o \x01(\x0b\x32#.PlayerSearchProto.SearchForm.Query\x1a\x15\n\x05Query\x12\x0c\n\x04text\x18\x01 \x01(\tb\x06proto3')



_PLAYERSEARCHPROTO = DESCRIPTOR.message_types_by_name['PlayerSearchProto']
_PLAYERSEARCHPROTO_SEARCHFORM = _PLAYERSEARCHPROTO.nested_types_by_name['SearchForm']
_PLAYERSEARCHPROTO_SEARCHFORM_QUERY = _PLAYERSEARCHPROTO_SEARCHFORM.nested_types_by_name['Query']
PlayerSearchProto = _reflection.GeneratedProtocolMessageType('PlayerSearchProto', (_message.Message,), {

  'SearchForm' : _reflection.GeneratedProtocolMessageType('SearchForm', (_message.Message,), {

    'Query' : _reflection.GeneratedProtocolMessageType('Query', (_message.Message,), {
      'DESCRIPTOR' : _PLAYERSEARCHPROTO_SEARCHFORM_QUERY,
      '__module__' : 'definitions.search_player_pb2'
      # @@protoc_insertion_point(class_scope:PlayerSearchProto.SearchForm.Query)
      })
    ,
    'DESCRIPTOR' : _PLAYERSEARCHPROTO_SEARCHFORM,
    '__module__' : 'definitions.search_player_pb2'
    # @@protoc_insertion_point(class_scope:PlayerSearchProto.SearchForm)
    })
  ,
  'DESCRIPTOR' : _PLAYERSEARCHPROTO,
  '__module__' : 'definitions.search_player_pb2'
  # @@protoc_insertion_point(class_scope:PlayerSearchProto)
  })
_sym_db.RegisterMessage(PlayerSearchProto)
_sym_db.RegisterMessage(PlayerSearchProto.SearchForm)
_sym_db.RegisterMessage(PlayerSearchProto.SearchForm.Query)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _PLAYERSEARCHPROTO._serialized_start=36
  _PLAYERSEARCHPROTO._serialized_end=199
  _PLAYERSEARCHPROTO_SEARCHFORM._serialized_start=109
  _PLAYERSEARCHPROTO_SEARCHFORM._serialized_end=199
  _PLAYERSEARCHPROTO_SEARCHFORM_QUERY._serialized_start=178
  _PLAYERSEARCHPROTO_SEARCHFORM_QUERY._serialized_end=199
# @@protoc_insertion_point(module_scope)
