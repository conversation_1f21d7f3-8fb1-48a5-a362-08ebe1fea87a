[project]
name = "ghunt"
version = "2.3.3"
authors = [
    {name = "mxrch", email = "<EMAIL>"},
]
description = "An offensive Google framework."
readme = "README.md"
requires-python = ">=3.10"
license = {text = "AGPL-3.0"}
classifiers = [
    "Programming Language :: Python :: 3",
]
dynamic = ["dependencies"]
keywords=["osint", "pentest", "cybersecurity", "investigation", "hideandsec", "malfrats"]

[tool.setuptools.dynamic]
dependencies = {file = ["requirements.txt"]}

[tool.poetry.scripts]
ghunt = "ghunt.ghunt:main"

[tool.setuptools.packages.find]
include = ["ghunt", "ghunt.*"]

[project.urls]
"Homepage" = "https://github.com/mxrch/GHunt"
"Bug Reports" = "https://github.com/mxrch/GHunt/issues"
"Funding" = "https://github.com/sponsors/mxrch"
"Source" = "https://github.com/mxrch/GHunt"

[tool.poetry]
name = "ghunt"
version = "2.3.3"
description = "An offensive Google framework."
authors = ["mxrch <<EMAIL>>"]
license = "AGPL-3.0"
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
geopy = "^2.4.1"
httpx = {extras = ["http2"], version = "^0.27.2"}
imagehash = "^4.3.1"
pillow = "^10.4.0"
python-dateutil = "^2.9.0.post0"
rich = "^13.9.1"
beautifultable = "^1.1.0"
beautifulsoup4 = "^4.12.3"
alive-progress = "^3.1.5"
protobuf = "^5.28.2"
autoslot = "^2022.12.1"
humanize = "^4.10.0"
inflection = "^0.5.1"
jsonpickle = "^3.3.0"
packaging = "^24.1"
rich-argparse = "^1.5.2"
dnspython = "^2.7.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
