default_file_capabilities = [
    'can_block_owner',
    'can_copy',
    'can_download',
    'can_print',
    'can_read',
    'can_remove_my_drive_parent'
]

default_folder_capabilities = [
    'can_block_owner',
    'can_download',
    'can_list_children',
    'can_print',
    'can_read',
    'can_remove_my_drive_parent'
]

request_fields = [
    'copyRequiresWriterPermission',
    'sourceAppId',
    'authorizedAppIds',
    'linkShareMetadata',
    'teamDriveId',
    'primaryDomainName',
    'approvalMetadata',
    'md5Checksum',
    'resourceKey',
    'quotaBytesUsed',
    'hasChildFolders',
    'fullFileExtension',
    'isAppAuthorized',
    'iconLink',
    'trashingUser',
    'title',
    'recency',
    'detectors',
    'exportLinks',
    'modifiedDate',
    'copyable',
    'description',
    'mimeType',
    'passivelySubscribed',
    'videoMediaMetadata',
    'headRevisionId',
    'customerId',
    'fileExtension',
    'originalFilename',
    'parents',
    'imageMediaMetadata',
    'recencyReason',
    'folderColorRgb',
    'createdDate',
    'labels',
    'abuseNoticeReason',
    'webViewLink',
    'driveId',
    'ownedByMe',
    'flaggedForAbuse',
    'lastModifyingUser',
    'thumbnailLink',
    'capabilities',
    'sharedWithMeDate',
    'primarySyncParentId',
    'sharingUser',
    'version',
    'permissionsSummary',
    'actionItems',
    'labelInfo',
    'explicitlyTrashed',
    'shared',
    'subscribed',
    'ancestorHasAugmentedPermissions',
    'writersCanShare',
    'permissions',
    'alternateLink',
    'hasLegacyBlobComments',
    'id',
    'userPermission',
    'hasThumbnail',
    'lastViewedByMeDate',
    'fileSize',
    'kind',
    'thumbnailVersion',
    'spaces',
    'organizationDisplayName',
    'abuseIsAppealable',
    'trashedDate',
    'folderFeatures',
    'webContentLink',
    'contentRestrictions',
    'shortcutDetails',
    'folderColor',
    'hasAugmentedPermissions'
]

mime_types = {
    "application/vnd.google-apps.audio": "Audio 🎧",
    "application/vnd.google-apps.document":	"Google Docs 📝",
    "application/vnd.google-apps.drive-sdk": "3rd party shortcut ↪️",
    "application/vnd.google-apps.drawing": "Google Drawing ✏️",
    "application/vnd.google-apps.file":	"Google Drive file 📄",
    "application/vnd.google-apps.folder": "Google Drive folder 🗂️",
    "application/vnd.google-apps.form":	"Google Forms 👨‍🏫",
    "application/vnd.google-apps.fusiontable": "Google Fusion Tables 🌶️",
    "application/vnd.google-apps.jam": "Google Jamboard 🖍️",
    "application/vnd.google-apps.map": "Google My Maps 📍",
    "application/vnd.google-apps.photo": "Photo 📷",
    "application/vnd.google-apps.presentation":	"Google Slides ❇️",
    "application/vnd.google-apps.script": "Google Apps Scripts 📜",
    "application/vnd.google-apps.shortcut":	"Shortcut ↩️",
    "application/vnd.google-apps.site":	"Google Sites 🌐",
    "application/vnd.google-apps.spreadsheet": "Google Sheets 📟",
    "application/vnd.google-apps.unknown": "Unknown ❔",
    "application/vnd.google-apps.video": "Video 📼",
    "application/pdf": "PDF Document 📕",
    "application/msword": "Microsoft Word document 📝",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "OpenXML Word document 📝",
    "application/vnd.ms-powerpoint.presentation.macroEnabled.12": "Microsoft Powerpoint with macros ❇️",
    "application/vnd.ms-excel": "Microsoft Excel spreadsheet 📟",
    "image/jpeg": "JPEG Image 🖼️",
    "audio/mpeg": "MPEG Audio 🎧",
    "video/mpeg": "MPEG Video 📼",
    "application/zip": "ZIP Archive 🗃️",
    "text/plain": "Plain Text 📃",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "OpenXML Spreadsheet document ❇️",
    "application/vnd.android.package-archive": "Android Package 📱",
    "application/vnd.google-apps.kix": "Google Apps 🈸"
}